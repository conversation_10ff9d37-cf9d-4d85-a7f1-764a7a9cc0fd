<odoo>
      <record id="th_product_manufacturing_tree_view" model="ir.ui.view">
        <field name="name">th_product_manufacturing_view</field>
        <field name="model">th.product.manufacturing</field>
        <field name="arch" type="xml">
            <tree string="sản phẩm sản xuất" >
                <field name="th_project_template_id"/>
                <field name="th_type"/>
                <field name="th_number_of_credits"/>
                <field name="th_units_used_id"/>
                <field name="th_standard_costs"/>
                <field name="th_proposed_costs"/>
                <field name="th_object_costs"/>
            </tree>
        </field>
    </record>

    <record id="th_product_manufacturing_form_view" model="ir.ui.view">
        <field name="name">th_product_manufacturing_form_view</field>
        <field name="model">th.product.manufacturing</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <field name="readonly_domain" invisible="1"/>
                    <group>
                        <group>
                            <field name="th_default_code"/>
                            <field name="th_project_template_id" required="1" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_term_code"/>
                            <field name="th_type"/>
                            <field name="th_units_used_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_project_lpm_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_major_ids" widget="many2many_tags" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_language"/>
                            <field name="th_object_type"/>
                        </group>
                        <group name='group_right'>
                            <field name="th_number_of_credits" required="1"/>
                            <field name="th_slot"/>
                            <field name="th_signal_costs_id"  options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_percent_gv"/>
                            <field name="th_production_standard_id" domain="th_production_standard_domain" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_production_standard_domain" invisible="1"/>
                            <field name="th_hourly_cost_parameter_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_production_time"/>
                            <field name="th_standard_costs" widget="monetary"/>
                            <field name="th_proposed_costs" widget="monetary"/>
                            <field name="th_object_costs" widget="monetary"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Thời gian theo tiêu chuẩn">
                            <group>
                                <group>
                                    <field name="th_day"  force_save="1"/>
                                    <field name="th_number_of_articles"  force_save="1"/>
                                    <field name="th_number_of_items" force_save="1"/>

                                </group>
                                <group>
                                    <field name="th_subject_duration"/>
                                    <field name="th_lesson_duration"/>
                                    <field name="th_item_duration"/>
                                </group>
                            </group>

                        </page>
                        <page string="Tham số đóng gói">
                            <group>
                                <group>
                                    <field name="th_packaging_lesson_ids" widget="many2many_tags" options="{'no_create': True, 'no_open':True}"/>
                                    <field name="th_packaging_section_ids" widget="many2many_tags" options="{'no_create': True, 'no_open':True}"/>
                                </group>
                                <group>
                                    <field name="th_packaging_object_ids" widget="many2many_tags"  options="{'no_create': True, 'no_open':True}"/>
                                </group>
                            </group>
                        </page>
                         <page string="Ghi chú">
                            <group>
                                 <field name="th_description"/>
                            </group>
                        </page>

                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>

        </field>
    </record>
     <record id="action_over_lpm_view_search_project" model="ir.ui.view">
        <field name="name">action_over_view_search_project</field>
        <field name="model">th.product.manufacturing</field>
        <field name="arch" type="xml">
            <search string="State">
                <group expand="0" string="Group By">
                    <filter string="Trường" name="th_units_used_id" context="{'group_by': 'th_units_used_id'}"/>
                    <filter string="Dự án" name="th_project_lpm_id" context="{'group_by': 'th_project_lpm_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="th_product_manufacturing_action" model="ir.actions.act_window">
        <field name="name">Học liệu sản xuất</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.product.manufacturing</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'default_th_project_lpm_id': active_id}</field>
        <field name="domain">[('th_project_lpm_id', '=', active_id)]</field>
    </record>

    <record id="th_product_manufacturing_lpm_action" model="ir.actions.act_window">
        <field name="name">Kế hoạch sản xuất</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.product.manufacturing</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{'search_default_th_units_used_id': True,'search_default_th_project_lpm_id': True}</field>
    </record>
    <record id="th_approve_products_manufacturing_action" model="ir.actions.act_window">
        <field name="name">Học liệu sản xuất</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.product.manufacturing</field>
        <field name="view_mode">tree,form</field>
        <field name="context">{}</field>
        <field name="domain">[]</field>
    </record>
</odoo>