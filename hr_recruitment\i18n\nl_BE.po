# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * hr_recruitment
# 
# Translators:
# <PERSON> <<EMAIL>>, 2017
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 11.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2017-11-16 08:08+0000\n"
"PO-Revision-Date: 2017-11-16 08:08+0000\n"
"Last-Translator: <PERSON> <<EMAIL>>, 2017\n"
"Language-Team: Dutch (Belgium) (https://www.transifex.com/odoo/teams/41243/nl_BE/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: nl_BE\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"\n"
"          <div style=\"background-color:#F3F5F6;color:#515166;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <table style=\"width:600px;margin:10px auto;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td>\n"
"                            <a href=\"/\">\n"
"                                <img src=\"/logo\" alt=\"${object.company_id.name}\" style=\"vertical-align:baseline;max-width:100px;\" />\n"
"                            </a>\n"
"                        </td>\n"
"                        <td style=\"text-align:right;vertical-align:middle;\">\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                                <a href=\"${object.job_id.website_url}\" style=\"background-color: #1abc9c; padding: 12px; font-weight: 12px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">Job Description</a>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:600px;margin:0px auto;background-color:white;padding:8px; border:1px solid #e1e1e1;\">\n"
"            <tr>\n"
"                <td style=\"font-size:16px; font-weight:300;\">\n"
"                    <p style=\"font-size:16px;\">\n"
"                        Hi,\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        Thank you for your interest in joining the\n"
"                        <b>${object.company_id.name}</b> team.  We wanted to\n"
"                        let you know that, although your resume is competitive,\n"
"                        our hiring team reviewed your application and <b>did not\n"
"                        select it for further consideration</b>.\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        Please note that recruiting is hard, and we can do\n"
"                        mistake. Do not hesitate to reply to this email if you\n"
"                        think we did a mistake, or if you want more information\n"
"                        about our decision.\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        We will, however, keep your resume on record and get in\n"
"                        touch with you about future opportunities that may be a\n"
"                        better fit for your skills and experience.\n"
"                    </p>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        We wish you all the best in your job search and hope we\n"
"                        will have the chance to consider you for another role\n"
"                        in the future.\n"
"                    </p>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        Regards,\n"
"                    </p>\n"
"\n"
"                    <p style=\"color:grey;\">-- <br/>\n"
"                    % if object.user_id:\n"
"                        <strong>${object.user_id.name}</strong><br/>\n"
"                        Email: ${object.user_id.email or ''}<br/>\n"
"                        Phone: ${object.user_id.phone or ''}\n"
"                    % else:\n"
"                        ${object.company_id.name}<br/>\n"
"                        The HR Team\n"
"                    % endif\n"
"                    </p>\n"
"\n"
"                </td>\n"
"            </tr>\n"
"            </table>\n"
"            <table style=\"width:600px;margin:0px auto;text-align:center;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding-top:10px;font-size: 12px;\">\n"
"                            <div>Sent by ${object.company_id.name}</div>\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                            <div>\n"
"                                Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">our others jobs</a>.\n"
"                            </div>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"\n"
"          </div>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"\n"
"          <div style=\"background-color:#F3F5F6;color:#515166;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <table style=\"width:600px;margin:10px auto;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td>\n"
"                            <a href=\"/\">\n"
"                                <img src=\"/logo\" alt=\"${object.company_id.name}\" style=\"vertical-align:baseline;max-width:100px;\" />\n"
"                            </a>\n"
"                        </td>\n"
"                        <td style=\"text-align:right;vertical-align:middle;\">\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                                <a href=\"${object.job_id.website_url}\" style=\"background-color: #1abc9c; padding: 12px; font-weight: 12px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">Job Description</a>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:600px;margin:0px auto;background-color:white;padding:8px;border:1px solid #e1e1e1;\">\n"
"            <tr>\n"
"                <td style=\"font-size:16px; font-weight:300;\">\n"
"                    <div style=\"text-align: center\">\n"
"                        <h2>Congratulations!</h2>\n"
"                        <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"                        <img src=\"/hr_recruitment/static/src/img/congratulations.png\" alt=\"Congratulations!\" style=\"width:175px;margin:20px 0;\"/>\n"
"                    </div>\n"
"\n"
"                    <p style=\"font-size:16px;\">\n"
"                        We just reviewed your resume, and it caught our\n"
"                        attention. As we think you might be great for the\n"
"                        position, your application has been short listed for a\n"
"                        call or an interview.\n"
"                    </p>\n"
"\n"
"                    % if object.user_id:\n"
"                    <p style=\"font-size:16px;\">\n"
"                        You will soon be contacted by:\n"
"                    </p>\n"
"                    <table style=\"width:100%; border:0px solid white;\">\n"
"                    <tr>\n"
"                        <td width=\"75\">\n"
"                            <img src=\"/web/image/res.users/${object.user_id.id}/image_small\" alt=\"Avatar\" style=\"vertical-align:baseline;max-width:64px;\" />\n"
"                        </td><td style=\"font-size:15px;\">\n"
"                            <strong>${object.user_id.name}</strong><br/>\n"
"                            <span>Email: ${object.user_id.email or ''}</span><br/>\n"
"                            <span>Phone: ${object.user_id.phone or ''}</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                    </table>\n"
"                    % endif\n"
"                    <p style=\"font-size:16px;margin:24px 0;\">\n"
"                        See you soon,\n"
"                    </p><p style=\"font-size:16px;color: grey;\">\n"
"                        -- <br/>\n"
"                        Odoo HR Team\n"
"                    </p>\n"
"\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        We usually <strong>answer applications within 3 days</strong>.\n"
"                        The next step is either a call or a meeting in our offices.\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        Feel free to <strong>contact us if you want a faster\n"
"                        feedback</strong> or if you don't get news from us\n"
"                        quickly enough (just reply to this email).\n"
"                    </p>\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Want to learn about us?</strong></h3>\n"
"                    <ol>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.odoo.com/blog/odoo-news-5/post/the-odoo-story-56\">Founders' story</a></li>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.slideshare.net/openobject/the-odoo-culture\">The Odoo Culture</a></li>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.odoo.com/page/about-us\">About us</a></li>\n"
"                    </ol>\n"
"\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"\n"
"                    % set location = ''\n"
"                    % if object.job_id.address_id.name:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\"><strong>${object.job_id.address_id.name}</strong></p>\n"
"                    % endif\n"
"                    % if object.job_id.address_id.street:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.street}</p>\n"
"                        % set location = object.job_id.address_id.street\n"
"                    % endif\n"
"                    % if object.job_id.address_id.street2:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.street2}</p>\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.street2)\n"
"                    % endif\n"
"                    <p style=\"margin:0px 0px 0px 0px;\">\n"
"                    % if object.job_id.address_id.city:\n"
"                        ${object.job_id.address_id.city},\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.city)\n"
"                    % endif\n"
"                    % if object.job_id.address_id.state_id.name:\n"
"                        ${object.job_id.address_id.state_id.name},\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.state_id.name)\n"
"                    % endif\n"
"                    % if object.job_id.address_id.zip:\n"
"                        ${object.job_id.address_id.zip}\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.zip)\n"
"                    % endif\n"
"                    </p>\n"
"                    % if object.job_id.address_id.country_id.name:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.country_id.name}</p>\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.country_id.name)\n"
"                    % endif\n"
"\n"
"                </td>\n"
"            </tr>\n"
"            </table>\n"
"\n"
"            % if object.job_id.address_id:\n"
"            <div style=\"width:598px;margin:0px auto;border-left:1px solid #dddddd;border-right:1px solid #dddddd;border-bottom:1px solid #dddddd;\">\n"
"                <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                    <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&size=598x200&maptype=roadmap&format=png&visual_refresh=true&markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\"/>\n"
"                </a>\n"
"            </div>\n"
"            % endif\n"
"            <table style=\"width:600px;margin:0px auto;text-align:center;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding-top:10px;font-size: 12px;\">\n"
"                            <div>Sent by ${object.company_id.name}</div>\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                            <div>\n"
"                                Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.\n"
"                            </div>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"\n"
"\n"
"          </div>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"\n"
"          <div style=\"background-color:#F3F5F6;color:#515166;font-family:Arial,Helvetica,sans-serif;font-size:14px;\">\n"
"            <table style=\"width:600px;margin:10px auto;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td>\n"
"                            <a href=\"/\">\n"
"                                <img src=\"/logo\" alt=\"${object.company_id.name}\" style=\"vertical-align:baseline;max-width:100px;\" />\n"
"                            </a>\n"
"                        </td>\n"
"                        <td style=\"text-align:right;vertical-align:middle;\">\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                                <a href=\"${object.job_id.website_url}\" style=\"background-color: #1abc9c; padding: 12px; font-weight: 12px; text-decoration: none; color: #fff; border-radius: 5px; font-size:16px;\">Job Description</a>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"            <table style=\"width:600px;margin:0px auto;background-color:white;padding:8px;border:1px solid #e1e1e1;\">\n"
"            <tr>\n"
"                <td style=\"font-size:16px; font-weight:300;\">\n"
"                    <p style=\"font-size:16px;\">\n"
"                        Hi,\n"
"                    </p><p style=\"font-size:16px;\">\n"
"                        We confirm we successfully received your application to the job\n"
"\n"
"                        \"<a href=\"${object.job_id.website_url or ''}\" style=\"color:#9A6C8E;\"><strong>${object.job_id.name}</strong></a>\"\n"
"                        at <strong>${object.company_id.name}</strong>.\n"
"                    </p>\n"
"                    <p style=\"font-size:16px;\">\n"
"                        We will come back to you shortly.\n"
"                    </p>\n"
"\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"                    % if object.user_id:\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <table style=\"width:100%; border:0px solid white;\">\n"
"                    <tr>\n"
"                        <td width=\"75\">\n"
"                            <img src=\"/web/image/res.users/${object.user_id.id}/image_small\" alt=\"Avatar\" style=\"vertical-align:baseline;max-width:64px;\" />\n"
"                        </td><td style=\"font-size:15px;\">\n"
"                            <strong>${object.user_id.name}</strong><br/>\n"
"                            <span>Email: ${object.user_id.email or ''}</span><br/>\n"
"                            <span>Phone: ${object.user_id.phone or ''}</span>\n"
"                        </td>\n"
"                    </tr>\n"
"                    </table>\n"
"                    % endif\n"
"\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                    <p>We usually <strong>answer applications within 3 days.</strong></p>\n"
"                    <p>\n"
"                        Feel free to <strong>contact us if you want a faster\n"
"                        feedback</strong> or if you don't get news from us\n"
"                        quickly enough (just reply to this email).\n"
"                    </p>\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Want to learn more?</strong></h3>\n"
"                    <ol>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.odoo.com/blog/odoo-news-5/post/the-odoo-story-56\">Founders' story</a></li>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.slideshare.net/openobject/the-odoo-culture\">The Odoo Culture</a></li>\n"
"                        <li><a style=\"color:#1CC5A9\" href=\"https://www.odoo.com/page/about-us\">About us</a></li>\n"
"                    </ol>\n"
"\n"
"                    <hr style=\"margin: 30px 0\"/>\n"
"\n"
"                    % set location = ''\n"
"                    % if object.job_id.address_id.name:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\"><strong>${object.job_id.address_id.name}</strong></p>\n"
"                    % endif\n"
"                    % if object.job_id.address_id.street:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.street}</p>\n"
"                        % set location = object.job_id.address_id.street\n"
"                    % endif\n"
"                    % if object.job_id.address_id.street2:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.street2}</p>\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.street2)\n"
"                    % endif\n"
"                    <p style=\"margin:0px 0px 0px 0px;\">\n"
"                    % if object.job_id.address_id.city:\n"
"                        ${object.job_id.address_id.city},\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.city)\n"
"                    % endif\n"
"                    % if object.job_id.address_id.state_id.name:\n"
"                        ${object.job_id.address_id.state_id.name},\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.state_id.name)\n"
"                    % endif\n"
"                    % if object.job_id.address_id.zip:\n"
"                        ${object.job_id.address_id.zip}\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.zip)\n"
"                    % endif\n"
"                    </p>\n"
"                    % if object.job_id.address_id.country_id.name:\n"
"                        <p style=\"margin:0px 0px 0px 0px;\">${object.job_id.address_id.country_id.name}</p>\n"
"                        % set location = '%s, %s' % (location, object.job_id.address_id.country_id.name)\n"
"                    % endif\n"
"\n"
"                </td>\n"
"            </tr>\n"
"            </table>\n"
"\n"
"            % if object.job_id.address_id:\n"
"            <div style=\"width:598px;margin:0px auto;border-left:1px solid #dddddd;border-right:1px solid #dddddd;border-bottom:1px solid #dddddd;\">\n"
"                <a href=\"https://maps.google.com/maps?q=${location}\" target=\"new\">\n"
"                    <img src=\"http://maps.googleapis.com/maps/api/staticmap?autoscale=1&size=598x200&maptype=roadmap&format=png&visual_refresh=true&markers=size:mid%7Ccolor:0xa5117d%7Clabel:%7C${location}\" style=\"vertical-align:bottom;\"/>\n"
"                </a>\n"
"            </div>\n"
"            % endif\n"
"            <table style=\"width:600px;margin:0px auto;text-align:center;\">\n"
"                <tbody>\n"
"                    <tr>\n"
"                        <td style=\"padding-top:10px;font-size: 12px;\">\n"
"                            <div>Sent by ${object.company_id.name}</div>\n"
"                            % if 'website_url' in object.job_id and object.job_id.website_url:\n"
"                            <div>\n"
"                                Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.\n"
"                            </div>\n"
"                            % endif\n"
"                        </td>\n"
"                    </tr>\n"
"                </tbody>\n"
"            </table>\n"
"\n"
"\n"
"          </div>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_employee
msgid ""
"\n"
"<p>Dear all,</p>\n"
"<p>I’m very pleased to announce that <strong> ${object.name} </strong> will be\n"
"joining us as a ${object.job_id.name}\n"
"%if object.department_id:\n"
"    in ${object.department_id.name}.\n"
"%endif\n"
".</p>\n"
"<p>Please welcome him/her and help him/her finding his/her marks.</p>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<b>Contact:</b>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<b>Degree:</b>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<b>Departement:</b>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "<span class=\"o_stat_text\">Meetings</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "<span>Folded in Recruitment Pipe: </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>New</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>Reporting</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>View</span>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_active
msgid "Active"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:20
#, python-format
msgid ""
"Add columns to define the <b>interview stages</b>.<br/><i>e.g. New &gt; "
"Qualified &gt; First Interview &gt; Recruited</i>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_address_id
msgid "Address where employees are working"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_id
msgid "Alias"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_contact
msgid "Alias Contact Security"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_alias_id
msgid "Alias ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_name
msgid "Alias Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_domain
msgid "Alias domain"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_model_id
msgid "Aliased Model"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event_applicant_id
msgid "Applicant"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_hired
msgid "Applicant hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_partner_name
msgid "Applicant's Name"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
msgid "Applicants"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"            If you install the document management modules, all resumes are indexed automatically,\n"
"            so that you can easily search through their content."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Application Summary"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application(s)"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_application_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_document_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_job_id
msgid "Applied Job"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_priority
msgid "Appreciation"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Archived"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_date_open
msgid "Assigned"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_attachment_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Attachments"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_template_id
msgid "Automated Email"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_availability
msgid "Availability"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_department
msgid "By Department"
msgstr ""

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_job
msgid "By Job"
msgstr ""

#. module: hr_recruitment
#: model:ir.filters,name:hr_recruitment.hr_applicant_filter_recruiter
msgid "By Recruiter"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Click here to create a new job position."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_no_employee
msgid "Click to add a new employee."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Click to add a new stage in the recruitment process."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Click to add a new tag."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_date_closed
msgid "Closed"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_color
msgid "Color Index"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Company"
msgstr "Bedrijf"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
msgid "Configuration"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:350
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_partner_id
#, python-format
msgid "Contact"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:352
#, python-format
msgid "Contact Email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Contract"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create & Edit"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Create Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Create alias"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid ""
"Create some aliases that will allow you to track where applicants come from."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_create_uid
msgid "Created by"
msgstr "Aangemaakt door"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_create_date
msgid "Created on"
msgstr "Aangemaakt op"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Creation Date"
msgstr "Aanmaakdatum"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Creation Week"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_activity
msgid "Currently there are not any activity scheduled."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_day_close
msgid "Days to Close"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_day_open
msgid "Days to Open"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_defaults
msgid "Default Values"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"            qualification call, first interview, second interview, refused,\n"
"            hired."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_type_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_name
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Degree of Recruitment"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_delay_close
msgid "Delay to Close"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_manager_id
msgid "Department Manager"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_description
msgid "Description"
msgstr "Omschrijving"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_display_name
msgid "Display Name"
msgstr "Schermnaam"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_documents_count
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Documents"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"            is different according to the job position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_email
msgid "Email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email alias"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_emp_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_employee_name
msgid "Employee Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_emp_id
msgid "Employee linked to the applicant."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_no_employee
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Employees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Event"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Excellent"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department_expected_employee
msgid "Expected Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_salary_expected
msgid "Expected Salary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_salary_expected_extra
msgid "Expected Salary Extra"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "Uitgebreide filters"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Extra advantages..."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Feedback of interviews..."
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_fold
msgid "Folded in Recruitment Pipe"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Future Activities"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_degree_sequence
msgid "Gives the sequence order when displaying a list of degrees."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage_sequence
msgid "Gives the sequence order when displaying a list of stages."
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Good"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Group By"
msgstr "Groeperen op"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
msgid "HR Department"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_hr_responsible_id
msgid "HR Responsible"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Hired Employees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage_template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings_module_hr_recruitment_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Interview Forms"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_job.py:94
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#, python-format
msgid "Job"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
msgid "Job Application Confirmation: ${object.job_id.name | safe}"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
msgid "Job Applications"
msgstr ""

#. module: hr_recruitment
#: model:utm.campaign,name:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Job Email"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_job_id
msgid "Job ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_address_id
msgid "Job Location"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
msgid "Job Position"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_config
msgid "Job Positions"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_job_id
msgid "Job Specific"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Jobs - Recruitment Form"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
msgid "Jobs Sources"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant___last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category___last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree___last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source___last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage___last_update
msgid "Last Modified on"
msgstr "Laatst gewijzigd op"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_last_stage_id
msgid "Last Stage"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Last Stage Update"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_write_uid
msgid "Last Updated by"
msgstr "Laatst bijgewerkt door"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_write_date
msgid "Last Updated on"
msgstr "Laatst bijgewerkt op"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Late Activities"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:16
#, python-format
msgid ""
"Let's have a look at the <b>applications pipeline</b> for this job position."
msgstr ""

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Manager"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_partner_mobile
msgid "Mobile"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "My Activities"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "My Applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_activity
msgid "My Next Activities"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category_name
msgid "Name"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department_new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
msgid "New Applicant"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
msgid "New Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
msgid "New Applications"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:426
#, python-format
msgid "New Employee %s Hired"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_employee_view_search
msgid "New Hired"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department_new_hired_employee
msgid "New Hired Employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_employee_action_from_department
msgid "Newly Hired Employees"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee_newly_hired_employee
msgid "Newly hired employee"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_nmenu_activity
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:368
#, python-format
msgid "No Subject"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_resumes
msgid "No document yet."
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Normal"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_attachment_number
msgid "Number of Attachments"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_delay_close
msgid "Number of days to close"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"            process and follow up all operations: meetings, interviews, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Offer"
msgstr ""

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
msgid ""
"Once a job position is created, you can track related applications\n"
"              and manage the recruitment process."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid ""
"Once a job position is created, you can track the applicants\n"
"                  and manage the recruitment process related to the job\n"
"                  position."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings_module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_user_id
msgid "Owner"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_parent_model_id
msgid "Parent Model"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_hr_responsible_id
msgid "Person responsible of validating the employee's contracts."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_partner_phone
msgid "Phone"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_probability
msgid "Probability"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_salary_proposed
msgid "Proposed Salary"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish available jobs on your website"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_alias_force_thread_id
msgid "Record Thread ID"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Recruitment"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Recruitment Done"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment Process"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job_user_id
msgid "Recruitment Responsible"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "Recruitments"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_reference
msgid "Referred By"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Refuse"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Refused"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Reopen Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reports"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_user_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Responsible"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_resumes
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job02
msgid "Resumes and Letters"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_salary_expected
msgid "Salary Expected by Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_form_view_job
msgid "Schedule interview with this applicant"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Search Applicants"
msgstr ""

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree_sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_sequence
msgid "Sequence"
msgstr "Reeks"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Settings"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_source_id
msgid "Source"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source_name
msgid "Source Name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_source_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_source
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage_job_id
msgid "Specific job that uses this stage. Other jobs will not use this stage."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Stage"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr ""

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage_name
msgid "Stage name"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Stage of Recruitment"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Start Recruitment"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Subject / Applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_name
msgid "Subject / Application Name"
msgstr ""

#. module: hr_recruitment
#: sql_constraint:hr.applicant.category:0
msgid "Tag name already exists !"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
msgid "Tags"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_availability
msgid "The date at which the applicant will be available to start working"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""

#. module: hr_recruitment
#: sql_constraint:hr.recruitment.degree:0
msgid "The name of the Degree of Recruitment must be unique!"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job_alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid ""
"These aliases can be emails or urls for every source. When the applicant "
"arrives here through one of these you'll know where he came from."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_email_cc
msgid ""
"These email addresses will be added to the CC field of all inbound and "
"outbound emails for this record before being sent. Separate multiple email "
"addresses with a comma"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant_email_from
msgid "These people will receive email."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "This Year"
msgstr "Dit jaar"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_resumes
msgid ""
"This menu helps you search through resumes and motivation\n"
"                letters. Odoo automatically indexes .PDF, .DOC, DOCX, .TXT\n"
"                files so that you can search keywords from the content of these\n"
"                files."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage_fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Today Activities"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:30
#, python-format
msgid ""
"Try to send an email to this address, it will create an application "
"automatically."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
msgid "Unassigned"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_crm_case_jobs_filter
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "Unread Messages"
msgstr "Ongelezen berichten"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_write_date
msgid "Update Date"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use interview forms during recruitment process"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on Survey app."
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:25
#, python-format
msgid "Use the breadcrumbs to <b>go back to the dashboard</b>."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_user_email
msgid "User Email"
msgstr ""

#. module: hr_recruitment
#: selection:hr.applicant,priority:0
msgid "Very Good"
msgstr ""

#. module: hr_recruitment
#. openerp-web
#: code:addons/hr_recruitment/static/src/js/tour.js:11
#, python-format
msgid "Want to <b>start recruiting</b> like a pro? <i>Start here.</i>"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_email_cc
msgid "Watchers Emails"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_employee
msgid "Welcome ${object.name} "
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_no_employee
msgid ""
"With just a quick glance on the Odoo employee screen, you\n"
"                can easily find all the information you need for each person;\n"
"                contact data, job position, availability, etc."
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:430
#, python-format
msgid "You must define an Applied Job and a Contact Name for this applicant."
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
msgid "Your Application: ${object.job_id.name | safe}"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: ${object.job_id.name | safe}"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr ""

#. module: hr_recruitment
#: code:addons/hr_recruitment/models/hr_recruitment.py:281
#, python-format
msgid "job applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "res.config.settings"
msgstr ""
