from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ThUserApprove(models.Model):
    _name = 'th.team'
    _description = "Đội nhóm"

    name = fields.Char(string="Tên đội nhóm")
    th_user_ids = fields.Many2many('res.users', string="Thành viên dự án")
    th_manager_id = fields.Many2one('res.users', string="Trưởng nhóm")

    # check không cho trùng tên đội nhóm
    @api.constrains('name')
    def _th_check_unique_team_name(self):
        for rec in self:
            if self.search_count([('name', '=', rec.name)]) > 1:
                raise ValidationError("Tên đội nhóm phải là duy nhất.")




