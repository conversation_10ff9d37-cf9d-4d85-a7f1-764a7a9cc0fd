<odoo>
   <record id="th_approve_project_kanban_view" model="ir.ui.view">
    <field name="name">th_approve_project_kanban_view</field>
    <field name="model">th.approve.project</field>
    <field name="arch" type="xml">
        <kanban default_group_by="state"  type="object" action="action_th_products_approve_manufacturing" sample="1">
            <field name="th_project_lpm_id"/>
            <field name="th_customers"/>
            <field name="th_start_date"/>
            <field name="th_end_date"/>
            <templates>
                <t t-name="kanban-box">
                    <div class="oe_kanban_global_click">
                        <div class="oe_kanban_details">
                            <strong class="o_kanban_record_title mb4">
                                <field name="th_project_lpm_code"/>
                            </strong>
                            <div class="o_kanban_record_subtitle mb4">
                                <field name="th_project_lpm_id"/>
                            </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="th_approve_project_tree_view" model="ir.ui.view">
        <field name="name">th_approve_project_tree_view</field>
        <field name="model">th.approve.project</field>
        <field name="arch" type="xml">
            <tree string="Phê dự án sản xuất" >
                <field name="th_project_lpm_code" optional="show"/>
                <field name="th_project_lpm_id" optional="show"/>
                <field name="th_team_id" optional="show"/>
                <field name="th_lead_the_project" optional="show"/>
                <field name="th_customers" optional="show"/>
                <field name="th_start_date" optional="show"/>
                <field name="th_end_date" optional="show"/>
                <field name="th_production_number" optional="show"/>
                <field name="th_total_object_costs" optional="show"/>
                <field name="th_total_proposed_costs" optional="show"/>
                <field name="th_cost_qa" optional="show"/>
                <field name="th_total_production_costs" optional="show"/>
                <field name="th_costs_incurred" optional="show"/>
                <field name="th_project_members" widget="many2many_tags" optional="show"/>
                <field name="th_implementation_unit" optional="show"/>
                <field name="state" string="Trạng thái phê duyệt" optional="show"/>
                <field name="th_state_approve" widget="many2many_tags" optional="show"/>
                <field name="th_state_pending" widget="many2many_tags" optional="show"/>
            </tree>
        </field>
    </record>
    <record id="th_approve_project_form_view" model="ir.ui.view">
        <field name="name">th_approve_project_form_view</field>
        <field name="model">th.approve.project</field>
        <field name="arch" type="xml">
            <form>
                <header>
                     <field name="state" widget="statusbar" statusbar_visible="draft,pending,approved,refused,cancel"/>
                     <field name="th_is_lpm2" invisible="1"/>
                    <button name="action_approved" type="object" string="Phê duyệt"  class="btn-primary"  confirm="Bạn có chắc muốn duyệt dự án này không này?" attrs="{'invisible': ['|','|',('th_hidden_button', '=', False),('state','in', ['draft','refused','approved','cancel']),('id', '=', False)]}"/>
                    <button name="action_refuse" type="object" string="Từ chối"  class="btn-primary"  confirm="Bạn có chắc muốn từ chối dự án này?" attrs="{'invisible': ['|','|',('th_hidden_button', '=', False),('state','in', ['draft','refused','approve','cancel']),('id', '=', False)]}"/>
                    <button name="action_pending" type="object" string="Gửi duyệt"  class="btn-primary"  confirm="Bạn có muốn gửi duyệt dự án này?" attrs="{'invisible': [('state', '!=', 'draft')]}"/>
                    <button name="action_cancel" type="object" string="Hủy"  class="btn-primary"  confirm="Bạn có muốn hủy dự án này?" attrs="{'invisible': [('state', 'in', ['draft','cancel'])]}"/>
                    <button name="action_draft" type="object" string="Đưa về dự thảo"  class="btn-primary"  confirm="Bạn có muốn đưa về dự thảo dự án này?" attrs="{'invisible': [('state', 'not in', ['cancel'])]}"/>
                    <button name="action_th_transfer_lpm2" type="object" string="Chuyển sang LPM2"  class="btn-primary"  confirm="Thao tác này không thể hoàn tác.Bạn chắc chắn chuyển sang LPM2 không?" attrs="{'invisible': ['|',('state', '!=', 'approved'),('th_is_lpm2', '=', True)]}"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="th_hidden_button"  invisible="1"/>
                            <field name="th_lead_domain" invisible="1"/>
                            <field name="th_members_domain" invisible="1"/>
                            <field name="th_project_lpm_code"  readonly="1"/>
                            <field name="th_project_lpm_id" string="Tên dự án*" required="1" attrs="{'readonly': [('state', '!=', 'draft')]}"  options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_implementation_unit"  attrs="{'readonly': [('state', '!=', 'draft')]}" />
                            <field name="th_customers"  readonly="1"/>
                            <field name="th_team_id" string="Đội nhóm" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_selected_lead" string="Chủ trì dự án" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_lead_domain"/>
                            <field name="th_selected_members" string="Thành viên dự án" widget="many2many_tags" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True,'no_edit': True, 'no_open':True}" domain="th_members_domain"/>
                            <field name="th_lead_the_project" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True,'no_edit': True, 'no_open':True}" invisible="1"/>
                            <field name="th_project_members" widget="many2many_tags" attrs="{'readonly': [('state', '!=', 'draft')]}" options="{'no_create': True,'no_edit': True, 'no_open':True}" invisible="1"/>
                        </group>
                        <group name='group_right'>
                            <field name="th_start_date"  attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="th_end_date"  attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="th_production_number" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                            <field name="th_total_production_costs" widget="monetary" attrs="{'readonly': [('state', '!=', 'draft')]}" string="Tổng chi phí sản xuất theo TCSX (Dự kiến)"/>
                            <field name="th_total_object_costs" widget="monetary"/>
                            <field name="th_total_proposed_costs" widget="monetary" />
                            <field name="th_cost_qa" widget="monetary" readonly="1"/>
                            <field name="th_costs_incurred" widget="monetary" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                     <page string="Danh mục sản xuất và đơn giá chi tiết">
                    <field name="th_product_manufacturing_ids">
                           <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_default_code" optional="show"/>
                                    <field name="th_units_used_id" optional="show"/>
                                    <field name="th_type" optional="show"/>
                                    <field name="th_major_ids" widget="many2many_tags" optional="show"/>
                                    <field name="th_term_code" optional="show"/>
                                    <field name="th_project_template_id" optional="show"/>
                                    <field name="th_project_lpm_id" optional="hide"/>
                                    <field name="th_number_of_credits" optional="hide"/>
                                    <field name="th_production_standard_id" optional="hide"/>
                                    <field name="th_standard_costs" optional="hide"/>
                                    <field name="th_object_costs" optional="hide"/>
                                    <field name="th_description" string="Ghi chú" optional="hide"/>
                           </tree>
                         </field>
                    </page>
                    <page string="Phạm vi dự án">
                        <field name="th_project_scope"  attrs="{'readonly': [('state', '!=', 'draft')]}"/>
                    </page>
                        <page string="Tiêu chuẩn học liệu">
                         <field name="th_production_standard_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_standard_code"/>
                                    <field name="th_value_attribute_ids" widget="many2many_tags"/>
                                    <field name="th_description_attribute"/>
                                </tree>
                            </field>
                    </page>
                    <page string="Những người phê duyệt">
                         <field name="th_user_approve_ids" attrs="{'readonly': [('state', '!=', 'draft')]}">
                                <tree editable="bottom">
                                    <field name="th_user_id"/>
                                    <field name="status" readonly="1"/>
                                </tree>
                            </field>
                    </page>

                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>

        </field>
    </record>
    <record id="th_approve_project_action" model="ir.actions.act_window">
        <field name="name">Phê duyệt dự án</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.approve.project</field>
        <field name="view_mode">tree,form</field>
    </record>
   <record id="action_approve_product" model="ir.actions.server">
        <field name="name">Phê duyệt sản phẩm</field>
        <field name="model_id" ref="approvals.model_approval_category"/>
           <field name="state">code</field>
        <field name="code">
           action = model.create_request_product()
        </field>
   </record>
    <record id="action_th_approve_project" model="ir.actions.act_window">
    <field name="name">Dự án</field>
    <field name="res_model">th.approve.project</field>
    <field name="view_mode">kanban</field>
    <field name="context">{'create': 0,'search_default_state': True}</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Tạo dự án sản xuất mới
        </p>
        <p>
            Tạo dự án sản xuất để quản lý và theo dõi tiến độ phê duyệt
        </p>
    </field>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('th_approve_project_kanban_view')}),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('th_approve_project_tree_view')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('th_approve_project_form_view')})]"/>
    </record>
</odoo>