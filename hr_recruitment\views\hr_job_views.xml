<odoo>

    <record model="ir.actions.act_window" id="action_hr_job_new_application">
        <field name="name">New Application</field>
        <field name="res_model">hr.applicant</field>
        <field name="view_mode">form</field>
        <field name="context">{'search_default_job_id': [active_id], 'default_job_id': active_id}</field>
    </record>

    <record id="view_hr_job_kanban" model="ir.ui.view">
        <field name="name">hr.job.kanban</field>
        <field name="model">hr.job</field>
        <field name="arch" type="xml">
            <kanban class="oe_background_grey o_kanban_dashboard o_hr_recruitment_kanban" on_create="hr_recruitment.create_job_simple" sample="1" limit="40" action="%(action_hr_job_applications)d" type="action">
                <field name="active"/>
                <field name="name"/>
                <field name="alias_name"/>
                <field name="alias_domain"/>
                <field name="is_favorite"/>
                <field name="department_id"/>
                <field name="no_of_recruitment"/>
                <field name="color"/>
                <field name="new_application_count"/>
                <field name="no_of_hired_employee"/>
                <field name="manager_id"/>
                <field name="user_id"/>
                <field name="application_count"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''}">
                            <div class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <field name="is_favorite" widget="boolean_favorite" nolabel="1"/>
                                    <div class="o_primary ps-3">
                                        <span class="o_text_overflow"><t t-esc="record.name.value"/></span>
                                    </div>
                                    <div class="ps-3 text-muted">
                                        <field name="user_id" />
                                    </div>
                                    <div class="ps-3 o_secondary" groups="base.group_multi_company">
                                        <small><i class="fa fa-building-o" role="img" aria-label="Company" title="Company"></i> <field name="company_id"/></small>
                                    </div>
                                    <div t-if="record.alias_name.value and record.alias_domain.value" class="ps-3 o_secondary o_job_alias">
                                        <small><i class="fa fa-envelope-o" role="img" aria-label="Alias" title="Alias"></i> <field name="alias_id"/></small>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section" groups="hr_recruitment.group_hr_recruitment_user">
                                    <a class="o_kanban_manage_toggle_button" href="#"><i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/></a>
                                </div>
                            </div>
                            <div class="container o_recruitment_job_container o_kanban_card_content mt-0 mt-sm-3">
                                <div class="row">
                                    <div class="col-7">
                                        <button class="btn btn-primary" name="%(action_hr_job_applications)d" type="action">
                                            <field name="new_application_count"/> New Applications
                                        </button>
                                    </div>
                                    <ul class="col-5 o_job_activities">
                                        <li>
                                            <a name="edit_job" type="edit" t-attf-class="{{ record.no_of_recruitment.raw_value > 0 ? 'to-recruit' : 'text-secondary' }}" groups="!hr_recruitment.group_hr_recruitment_interviewer">
                                                <field name="no_of_recruitment"/> To Recruit
                                            </a>
                                            <span t-attf-class="{{ record.no_of_recruitment.raw_value > 0 ? 'to-recruit' : 'text-secondary' }}" groups="hr_recruitment.group_hr_recruitment_interviewer">
                                                <field name="no_of_recruitment"/> To Recruit
                                            </span>
                                        </li>
                                        <li t-if="record.application_count.raw_value > 0">
                                            <field name="application_count"/> Applications
                                        </li>
                                        <li class="text-warning" t-if="record.activities_today.raw_value > 0">
                                            <a name="action_open_today_activities" type="object" class="text-warning"><field name="activities_today"/> Activities Today</a>
                                        </li>
                                        <li t-if="record.activities_overdue.raw_value > 0">
                                            <a name="action_open_late_activities" type="object" class="text-danger"><field name="activities_overdue"/> Late Activities</a>
                                        </li>
                                    </ul>
                                </div>
                                <div name="kanban_boxes" class="row flex-nowrap" groups="hr_recruitment.group_hr_recruitment_user"></div>
                            </div>
                            <div class="container o_recruitment_job_container o_kanban_card_manage_pane dropdown-menu" role="menu">
                                <div class="row">
                                    <div class="col-6 o_kanban_card_manage_section">
                                        <div role="menuitem" class="o_kanban_card_manage_title">
                                            <span>View</span>
                                        </div>
                                        <div role="menuitem" name="menu_view_applications">
                                            <a name="%(action_hr_job_applications)d" type="action">Applications</a>
                                        </div>
                                        <div role="menuitem">
                                            <a name="action_open_activities" type="object">Activities</a>
                                        </div>
                                        <div role="menuitem">
                                            <a name="%(action_hr_job_sources)d" type="action" context="{'default_job_id': active_id}">Trackers</a>
                                        </div>
                                    </div>
                                    <div class="col-6 o_kanban_card_manage_section">
                                        <div role="menuitem" class="o_kanban_card_manage_title">
                                            <span>New</span>
                                        </div>
                                        <div role="menuitem" name="menu_new_applications">
                                            <a name="%(hr_recruitment.action_hr_applicant_new)d" type="action">Application</a>
                                        </div>
                                    </div>
                                    <div class="col-6 o_kanban_card_manage_section">
                                        <div role="menuitem" class="o_kanban_card_manage_title">
                                            <span>Reporting</span>
                                        </div>
                                        <div role="menuitem" name="o_kanban_job_reporting">
                                            <a name="%(hr_recruitment.action_hr_recruitment_report_filtered_job)d" type="action">Analysis</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="o_kanban_card_manage_settings row">
                                    <div class="col-6" role="menuitem" aria-haspopup="true">
                                        <ul class="oe_kanban_colorpicker" data-field="color" role="popup"/>
                                    </div>
                                    <div class="col-6" role="menuitem">
                                        <a class="dropdown-item" t-if="widget.editable" name="edit_job" type="edit">Configuration</a>
                                        <a class="dropdown-item" t-if="record.active.raw_value" name="toggle_active" type="object">Archive</a>
                                        <a class="dropdown-item" t-if="!record.active.raw_value" name="toggle_active" type="object">Unarchive</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <record id="hr_job_search_view" model="ir.ui.view">
        <field name="name">hr.job.search</field>
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_job_filter" />
        <field name="priority">20</field>
        <field name="arch" type="xml">
            <xpath expr="//filter[@name='my_favorite_jobs']" position="after">
                <filter string="My Job Positions" name="my_positions" domain="[('user_id', '=', uid)]"/>
            </xpath>
        </field>
    </record>

    <record id="hr_job_view_tree_inherit" model="ir.ui.view">
        <field name="model">hr.job</field>
        <field name="inherit_id" ref="hr.view_hr_job_tree"/>
        <field name="arch" type="xml">
            <field name="department_id" position="after">
                <field name="application_count" string="Applications"/>
            </field>
            <field name="no_of_recruitment" position="after">
                <field name="alias_name" invisible="1"/>
                <field name="alias_id" attrs="{'invisible': [('alias_name', '=', False)]}" optional="hide"/>
                <field name="user_id" widget="many2one_avatar_user" optional="hide"/>
            </field>
        </field>
    </record>

    <!-- hr related job position menu action -->
    <record model="ir.actions.act_window" id="action_hr_job">
        <field name="name">Job Positions</field>
        <field name="res_model">hr.job</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="hr_recruitment.view_hr_job_kanban"/>
        <field name="view_ids" eval="[(5, 0, 0),
            (0, 0, {'view_mode': 'kanban', 'view_id': ref('hr_recruitment.view_hr_job_kanban')}),
            (0, 0, {'view_mode': 'tree', 'view_id': ref('hr_recruitment.hr_job_view_tree_inherit')}),
            (0, 0, {'view_mode': 'form', 'view_id': ref('hr.view_hr_job_form')})]"/>
        <field name="context">{}</field>
        <field name="help" type="html">
          <p class="o_view_nocontent_smiling_face">
              Ready to recruit more efficiently?
          </p><p>
              Let's create a job position.
          </p>
        </field>
    </record>
    <menuitem name="By Job Positions" parent="menu_crm_case_categ0_act_job"
        id="menu_hr_job_position" action="action_hr_job"
        sequence="1" groups="hr_recruitment.group_hr_recruitment_user" />

    <!-- Job Positions filtered for interviewers -->
    <record model="ir.actions.act_window" id="action_hr_job_interviewer">
        <field name="name">Job Positions</field>
        <field name="res_model">hr.job</field>
        <field name="view_mode">kanban,form</field>
        <field name="view_id" ref="hr_recruitment.view_hr_job_kanban"/>
        <field name="context">{'create': False}</field>
        <field name="domain">[
            '|',
                ('interviewer_ids', 'in', uid),
                ('extended_interviewer_ids', 'in', uid),
        ]</field>
    </record>
    <menuitem name="By Job Positions" parent="menu_crm_case_categ0_act_job"
        id="menu_hr_job_position_interviewer" action="action_hr_job_interviewer"
        sequence="1" groups="hr_recruitment.group_hr_recruitment_interviewer" />
</odoo>
