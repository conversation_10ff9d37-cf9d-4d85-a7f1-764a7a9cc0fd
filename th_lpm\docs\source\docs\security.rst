<PERSON>ân quyền người dùng
===================

1. C<PERSON>u trúc phân quyền
---------------------

Ba nhóm quyền chính:

* <PERSON><PERSON><PERSON> viên (group_lpm_user): <PERSON><PERSON><PERSON><PERSON> c<PERSON> bản, t<PERSON><PERSON> cậ<PERSON> chức năng thông thường
* <PERSON><PERSON><PERSON><PERSON>ý (group_lpm_leader): <PERSON><PERSON> thừa quyền <PERSON><PERSON> viê<PERSON>, thêm quyền phê duyệt
* Quản trị viên (group_lpm_administrator): <PERSON><PERSON> thừa quyền <PERSON>u<PERSON> lý, thêm quyền cấu hình hệ thống

2. <PERSON><PERSON> quyền theo menu
----------------------

* **Nhân viên:** <PERSON><PERSON><PERSON><PERSON> lý dự <PERSON>n, Ti<PERSON><PERSON> chu<PERSON><PERSON> sản xuất, Ti<PERSON><PERSON> chuẩn đóng gói
* **<PERSON><PERSON><PERSON><PERSON> lý:** Thêm menu Phê duyệt sản phẩm
* **Qu<PERSON><PERSON> trị viên:** Thêm menu Cấu hình (<PERSON><PERSON> <PERSON><PERSON><PERSON> học liệu, Th<PERSON>nh phần học liệu, Tham số sản xuất...)

3. Phân quyền theo hành động
--------------------------

* Phê duy<PERSON>t/từ chối tiêu chuẩn: Chỉ Quản lý và Quản trị viên
* Tiêu chuẩn sản xuất:

  * Nhân viên chỉ thấy tiêu chuẩn do họ tạo hoặc được chia sẻ
  * Quản lý/Quản trị viên thấy tất cả tiêu chuẩn đã phê duyệt

4. Hạn chế sau phê duyệt
-----------------------

* Không thể thêm sản phẩm vào dự án đã phê duyệt
* Các trường chuyển sang chế độ chỉ đọc sau phê duyệt
* Quy trình kiểm soát: đảm bảo dữ liệu không bị thay đổi sau khi phê duyệt