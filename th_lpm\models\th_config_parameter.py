from odoo import models, fields


class ThProductionParameters(models.Model):
    _name = 'th.production.parameters'
    _description = "Tham số sản xuất"
    _rec_name = 'display_name'

    display_name = fields.Char(string="Tên hiển thị")
    name = fields.Char(string="Tên", required=True)
    th_time = fields.Integer(string="Thời gian")
    th_unit = fields.Char(string="Đơn vị tính")
    is_subject_parameter = fields.Boolean(string="Là tham số môn")

class ThPackagingStandard(models.Model):
    _name = 'th.packaging.standard'
    _description = "Tiêu chuẩn đóng gói"
    _rec_name = 'display_name'

    display_name = fields.Char(string="Tên hiển thị")
    name = fields.Char(string="Tên", required=True)
    th_quantity = fields.Integer(string="Số lượng")
    th_unit = fields.Char(string="Đơn vị tính")
    is_post_parameters = fields.<PERSON><PERSON><PERSON>(string="Là tham số bài")
    is_item_parameter = fields.<PERSON><PERSON>an(string="Là tham số mục")


class ThHourlyCostParameter(models.Model):
    _name = 'th.hourly.cost.parameter'
    _description = "Tham số chi phí theo giờ"
    _rec_name = 'th_price'

    name = fields.Char(string="Tên", required=True)
    th_price = fields.Integer(string="Mức giá")
    th_unit = fields.Char(string="Đơn vị tính")
    th_description = fields.Text(string="Mô tả")


class ThSignalCosts(models.Model):
    _name = 'th.signal.costs'
    _description = "Tham số chi phí tín chỉ"
    _rec_name = 'th_price'

    name = fields.Char(string="Tên", required=True)
    th_price = fields.Integer(string="Mức giá")
    th_unit = fields.Char(string="Đơn vị tính")
    th_description = fields.Text(string="Mô tả")




