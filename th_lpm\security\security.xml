<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="module_category_lpm_management" model="ir.module.category">
        <field name="name">Qu<PERSON>n lý Kế hoạch sản xuất</field>
        <field name="description"><PERSON><PERSON> quyền quản lý LPM</field>
        <field name="sequence">30</field>
    </record>

    <record id="module_category_lpm" model="ir.module.category">
        <field name="parent_id" ref="module_category_lpm_management"/>
        <field name="name">LPM</field>
        <field name="sequence">30</field>
    </record>

    <record id="group_lpm_user" model="res.groups">
        <field name="name">Nhân viên</field>
        <field name="category_id" ref="module_category_lpm"/>
        <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
    </record>

    <record id="group_lpm_leader" model="res.groups">
        <field name="name">Quản lý</field>
        <field name="category_id" ref="module_category_lpm"/>
        <field name="implied_ids" eval="[(4, ref('group_lpm_user'))]"/>
    </record>

    <record id="group_lpm_administrator" model="res.groups">
        <field name="name">Quản trị viên</field>
        <field name="category_id" ref="module_category_lpm"/>
        <field name="implied_ids" eval="[(4, ref('group_lpm_leader'))]"/>
    </record>

</odoo>