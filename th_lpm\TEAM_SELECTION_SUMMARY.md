# Tóm tắt chức năng chọn đội nhóm trong th_approve_project

## <PERSON><PERSON> tả chức năng
Thêm trường "Đội nhóm" vào form phê duyệt dự án, cho phép:
1. <PERSON><PERSON><PERSON> đội nhóm trước
2. Chọn chủ trì dự án từ danh sách trưởng nhóm + thành viên trong đội nhóm
3. Chọn thành viên dự án từ danh sách trưởng nhóm + thành viên trong đội nhóm (trừ chủ trì đã chọn)

## Các thay đổi đã thực hiện

### 1. Model th.approve.project (th_lpm/models/th_approve.py)

**Thêm trường mới:**
```python
th_team_id = fields.Many2one('th.team', string='Đội nhóm')
th_lead_domain = fields.Char(compute='_compute_lead_domain', string='Domain chủ trì')
th_members_domain = fields.Char(compute='_compute_members_domain', string='Domain thành viên')
```

**Thêm method compute:**
- `_compute_lead_domain()`: Tính domain cho chủ trì dựa trên đội nhóm
- `_compute_members_domain()`: Tính domain cho thành viên dựa trên đội nhóm và loại trừ chủ trì

**Thêm method onchange:**
- `_onchange_th_team_id()`: Reset chủ trì và thành viên khi đổi đội nhóm
- `_onchange_th_lead_the_project()`: Reset thành viên khi đổi chủ trì

### 2. Form View (th_lpm/views/th_approve_project_view.xml)

**Thêm vào form:**
- Trường `th_team_id` để chọn đội nhóm
- Domain cho `th_lead_the_project` và `th_project_members`
- Các trường domain ẩn để lưu trữ domain

**Thêm vào tree view:**
- Trường `th_team_id` để hiển thị đội nhóm

## Logic hoạt động

1. **Chọn đội nhóm**: 
   - Domain cho chủ trì = [trưởng nhóm] + [tất cả thành viên]
   - Reset chủ trì và thành viên hiện tại

2. **Chọn chủ trì**:
   - Domain cho thành viên = [trưởng nhóm] + [tất cả thành viên] - [chủ trì đã chọn]
   - Reset thành viên hiện tại

3. **Chọn thành viên**:
   - Cập nhật trực tiếp vào th_project_lpm_id thông qua related field

## Ưu điểm của cách tiếp cận này

- Sử dụng lại các trường related hiện có
- Không tạo trường dư thừa
- Dữ liệu tự động đồng bộ với th.project.lpm
- Logic đơn giản và dễ hiểu
