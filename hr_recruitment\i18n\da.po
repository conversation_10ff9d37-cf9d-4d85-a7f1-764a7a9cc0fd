# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* hr_recruitment
# 
# Translators:
# <PERSON><PERSON> <<EMAIL>>, 2022
# <AUTHOR> <EMAIL>, 2022
# <PERSON><PERSON>, 2022
# <PERSON><PERSON>, 2022
# <PERSON>, 2022
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 16.0+e\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2022-12-15 12:49+0000\n"
"PO-Revision-Date: 2022-09-22 05:52+0000\n"
"Last-Translator: <PERSON>, 2022\n"
"Language-Team: Danish (https://www.transifex.com/odoo/teams/41243/da/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: da\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "1 Meeting"
msgstr "1 Møde"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Click to view</b> the application."
msgstr "<b>Klik for at vise</b> ansøgningen."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Did you apply by sending an email?</b> Check incoming applications."
msgstr "<b>Ansøgte du ved at sende en email?</b> Tjek indgående ansøgninger."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "<b>Drag this card</b>, to qualify him for a first interview."
msgstr ""
"<b>Træk dette kort</b> for at kvalificere vedkommende til første interview."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"<div><b>Try to send an email</b> to the applicant.</div><div><i>Tips: All "
"emails sent or received are saved in the history here</i>"
msgstr ""
"<div><b>Prøv at sende en email</b> til ansøgeren.</div><div><i>Råd: Alle "
"emails afsendt eller modtaget gemmes her i historikken</i>"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"<div>Great job! You hired a new colleague!</div><div>Try the Website app to "
"publish job offers online.</div>"
msgstr ""
"<div>Fantastisk arbejde! Du ansatte en ny kollega!</div><div>Prøv at udgive "
"job tilbud online via Hjemmeside applikationen.</div>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-building-o\" role=\"img\" aria-label=\"Company\" title=\"Company\"/>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"
msgstr "<i class=\"fa fa-ellipsis-v\" role=\"img\" aria-label=\"Manage\" title=\"Manage\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"
msgstr "<i class=\"fa fa-envelope-o\" role=\"img\" aria-label=\"Alias\" title=\"Alias\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-mobile mr4\" role=\"img\" aria-label=\"Mobile\" title=\"Mobile\"/>"
msgstr "<i class=\"fa fa-mobile mr4\" role=\"img\" aria-label=\"Mobil\" title=\"Mobil\"/> "

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Documents\"/>"
msgstr "<i class=\"fa fa-paperclip\" role=\"img\" aria-label=\"Dokumenter\"/> "

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"<span attrs=\"{'invisible': [('address_id', '!=', False)]}\" "
"class=\"oe_read_only\">Remote</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"<span attrs=\"{'invisible': [('is_warning_visible', '=', False)]}\">\n"
"                            <span class=\"fa fa-exclamation-triangle text-danger ps-3\">\n"
"                            </span>\n"
"                            <span class=\"text-danger\">\n"
"                                All applications will lose their hired date and hired status.\n"
"                            </span>\n"
"                        </span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid ""
"<span attrs=\"{'invisible':[('salary_expected_extra','=',False)]}\"> + "
"</span>"
msgstr ""
"<span attrs=\"{'invisible':[('salary_expected_extra','=',False)]}\"> + "
"</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid ""
"<span attrs=\"{'invisible':[('salary_proposed_extra','=',False)]}\"> + "
"</span>"
msgstr ""
"<span attrs=\"{'invisible':[('salary_proposed_extra','=',False)]}\"> + "
"</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid ""
"<span class=\"badge rounded-pill text-bg-danger float-end me-4\" "
"attrs=\"{'invisible': ['|', ('active', '=', True), ('refuse_reason_id', '=',"
" False)]}\">Refused</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "<span class=\"bg-success\">Hired</span>"
msgstr "<span class=\"bg-success\">Ansat</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"
msgstr ""
"<span class=\"fa fa-lg fa-building-o\" title=\"Values set here are company-"
"specific.\"/>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "<span class=\"o_stat_text\">Trackers</span>"
msgstr "<span class=\"o_stat_text\">Sporinger</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "<span class=\"o_stat_value\">Employee</span>"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_recruitment_stage_kanban
msgid "<span>Folded in Recruitment Pipe: </span>"
msgstr "<span>Foldet i Ansættelses pipe: </span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>New</span>"
msgstr "<span>Ny</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Rapportering</span>"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "<span>View</span>"
msgstr "<span>Visning</span>"

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_congratulations
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,\n"
"                <br/><br/>\n"
"                We confirm we successfully received your application for the job\n"
"                \"<a t-att-href=\"hasattr(object.job_id, 'website_url') and object.job_id.website_url or ''\" style=\"color:#9A6C8E;\"><strong t-out=\"object.job_id.name or ''\">Experienced Developer</strong></a>\" at <strong t-out=\"object.company_id.name or ''\">YourCompany</strong>.\n"
"                <br/><br/>\n"
"                We will come back to you shortly.\n"
"\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <t t-if=\"object.user_id\">\n"
"                    <h3 style=\"color:#9A6C8E;\"><strong>Your Contact:</strong></h3>\n"
"                    <p>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                        <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    </p>\n"
"                    <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                </t>\n"
"\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days.</strong><br/><br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_interest
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"background-color: white; border-collapse: collapse; margin-left: 20px;\">\n"
"    <tr>\n"
"        <td valign=\"top\" style=\"padding: 0px 10px;\">\n"
"            <div style=\"text-align: center\">\n"
"                <h2>Congratulations!</h2>\n"
"                <div style=\"color:grey;\">Your resume has been positively reviewed.</div>\n"
"            </div>\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                We just reviewed your resume, and it caught our\n"
"                attention. As we think you might be great for the\n"
"                position, your application has been short listed for a\n"
"                call or an interview.\n"
"                <br/><br/>\n"
"                <div t-if=\"'website_url' in object.job_id and object.job_id.website_url\" style=\"padding: 16px 8px 16px 8px;\">\n"
"                    <a t-att-href=\"object.job_id.website_url\" style=\"background-color: #875a7b; text-decoration: none; color: #fff; padding: 8px 16px 8px 16px; border-radius: 5px;\">Job Description</a>\n"
"                </div>\n"
"\n"
"                <t t-if=\"object.user_id\">\n"
"                    You will soon be contacted by:<br/>\n"
"                    <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                    <span>Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t></span><br/>\n"
"                    <span>Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t></span>\n"
"                    <br/><br/>\n"
"                </t>\n"
"                See you soon,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    -- <br/>\n"
"                    The HR Team\n"
"                    <t t-if=\"'website_url' in object.job_id and hasattr(object.job_id, 'website_url') and object.job_id.website_url\">\n"
"                        Discover <a href=\"/jobs\" style=\"text-decoration:none;color:#717188;\">all our jobs</a>.<br/>\n"
"                    </t>\n"
"                </div>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 16px 0px 16px 0px;\"/>\n"
"                <h3 style=\"color:#9A6C8E;\"><strong>What is the next step?</strong></h3>\n"
"                We usually <strong>answer applications within a few days</strong>.\n"
"                <br/><br/>\n"
"                The next step is either a call or a meeting in our offices.\n"
"                <br/>\n"
"                Feel free to <strong>contact us if you want a faster\n"
"                feedback</strong> or if you don't get news from us\n"
"                quickly enough (just reply to this email).\n"
"                <br/>\n"
"\n"
"                <hr width=\"97%\" style=\"background-color: rgb(204,204,204); border: medium none; clear: both; display: block; font-size: 0px; min-height: 1px; line-height: 0; margin: 17px 0px 16px 0px;\"/>\n"
"                <t t-set=\"location\" t-value=\"''\"/>\n"
"                <t t-if=\"object.job_id.address_id.name\">\n"
"                    <strong t-out=\"object.job_id.address_id.name or ''\">Teksa SpA</strong><br/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street\">\n"
"                    <t t-out=\"object.job_id.address_id.street or ''\">Puerto Madero 9710</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"object.job_id.address_id.street\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.street2\">\n"
"                    <t t-out=\"object.job_id.address_id.street2 or ''\">Of A15, Santiago (RM)</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.street2)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.city\">\n"
"                    <t t-out=\"object.job_id.address_id.city or ''\">Pudahuel</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.city)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.state_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.state_id.name or ''\">C1</t>,\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.state_id.name)\"/>\n"
"                </t>\n"
"                <t t-if=\"object.job_id.address_id.zip\">\n"
"                    <t t-out=\"object.job_id.address_id.zip or ''\">98450</t>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.zip)\"/>\n"
"                </t>\n"
"                <br/>\n"
"                <t t-if=\"object.job_id.address_id.country_id.name\">\n"
"                    <t t-out=\"object.job_id.address_id.country_id.name or ''\">Argentina</t><br/>\n"
"                    <t t-set=\"location\" t-value=\"'%s, %s' % (location, object.job_id.address_id.country_id.name)\"/>\n"
"                </t>\n"
"                <br/>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_not_interested
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Dear,<br/><br/>\n"
"                We would like to thank you for your interest and your time.<br/>\n"
"                We wish you all the best in your future endeavors.\n"
"                <br/><br/>\n"
"                Best<br/>\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Marc Demo</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team<br/>\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:mail.template,body_html:hr_recruitment.email_template_data_applicant_refuse
msgid ""
"<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\" width=\"590\" style=\"min-width: 590px; background-color: white; padding: 0px 8px 0px 8px; border-collapse:separate;\">\n"
"    <tr>\n"
"        <td valign=\"top\">\n"
"            <div style=\"font-size: 13px; margin: 0px; padding: 0px;\">\n"
"                Hello,<br/><br/>\n"
"                Thank you for your interest in joining the\n"
"                <b><t t-out=\"object.company_id.name or ''\">YourCompany</t></b> team.  We\n"
"                wanted to let you know that, although your resume is\n"
"                competitive, our hiring team reviewed your application\n"
"                and <b>did not select it for further consideration</b>.\n"
"                <br/><br/>\n"
"                Please note that recruiting is hard, and we can make\n"
"                mistakes. Do not hesitate to reply to this email if you\n"
"                think we made a mistake, or if you want more information\n"
"                about our decision.\n"
"                <br/><br/>\n"
"                We will, however, keep your resume on record and get in\n"
"                touch with you about future opportunities that may be a\n"
"                better fit for your skills and experience.\n"
"                <br/><br/>\n"
"                We wish you all the best in your job search and hope we\n"
"                will have the chance to consider you for another role\n"
"                in the future.\n"
"                <br/><br/>\n"
"                Thank you,\n"
"                <div style=\"font-size: 11px; color: grey;\">\n"
"                    <t t-if=\"object.user_id\">\n"
"                        -- <br/>\n"
"                        <strong t-out=\"object.user_id.name or ''\">Mitchell Admin</strong><br/>\n"
"                        Email: <t t-out=\"object.user_id.email or ''\"><EMAIL></t><br/>\n"
"                        Phone: <t t-out=\"object.user_id.phone or ''\">******-123-4567</t>\n"
"                    </t>\n"
"                    <t t-else=\"\">\n"
"                        -- <br/>\n"
"                        <t t-out=\"object.company_id.name or ''\">YourCompany</t><br/>\n"
"                        The HR Team\n"
"                    </t>\n"
"                </div>\n"
"            </div>\n"
"        </td>\n"
"    </tr>\n"
"</table>\n"
"        "
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"En Python-ordbog, der vil blive evalueret for at angive standardværdier, når"
" du opretter nye poster til dette alias."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction
msgid "Action Needed"
msgstr "Handling påkrævet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__active
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__active
msgid "Active"
msgstr "Aktiv"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_activities
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities"
msgstr "Aktiviteter"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_overdue
msgid "Activities Overdue"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__activities_today
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Activities Today"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Aktivitet undtagelse markering"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_state
msgid "Activity State"
msgstr "Aktivitetstilstand"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivitets Type Ikon"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.mail_activity_type_action_config_hr_applicant
#: model:ir.ui.menu,name:hr_recruitment.hr_recruitment_menu_config_activity_type
msgid "Activity Types"
msgstr "Aktivitetstyper"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid "Add a new stage in the recruitment process"
msgstr "Tilføj et nyt stadie i ansættelses processen"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_category_action
msgid "Add a new tag"
msgstr "Tilføj et nyt tag"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__address_id
msgid "Address where employees are working"
msgstr "Adresse hvor medarbejdere arbejder"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_manager
msgid "Administrator"
msgstr "Administrator"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_id
msgid "Alias"
msgstr "Alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_contact
msgid "Alias Contact Security"
msgstr "Alias kontakt sikkerhed"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__alias_id
msgid "Alias ID"
msgstr "Alias ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_name
msgid "Alias Name"
msgstr "Alias navn"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_domain
msgid "Alias domain"
msgstr "Alias domæne"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_model_id
msgid "Aliased Model"
msgstr "Aliased model"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__all_application_count
msgid "All Application Count"
msgstr "Samlet Antal Ansøgninger"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ_all_app
msgid "All Applications"
msgstr "Alle ansøgninger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Analysis"
msgstr "Analyse"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_applicant_new
#: model:ir.model,name:hr_recruitment.model_hr_applicant
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_calendar_event__applicant_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__applicant_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "Applicant"
msgstr "Ansøger"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_degree
msgid "Applicant Degree"
msgstr "Ansøger uddannelsesgrad"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_hired
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_hired
msgid "Applicant Hired"
msgstr "Ansøger ansat"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_stage_changed
msgid "Applicant Stage Changed"
msgstr "Ansøger status ændret"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_new
msgid "Applicant created"
msgstr "Ansøger oprettet"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__email_from
msgid "Applicant email"
msgstr "Ansøger email"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_hired
msgid "Applicant hired"
msgstr "Ansøger ansat"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_hired_template
msgid "Applicant hired<br/>"
msgstr "Ansøger ansat<br/>"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_name
msgid "Applicant's Name"
msgstr "Ansøgers navn"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__applicant_without_email
msgid "Applicant(s) not having email"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_tree_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_calendar_view
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_activity
msgid "Applicants"
msgstr "Ansøgere"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__applicant_hired
msgid "Applicants Hired"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"                If you install the document management modules, all resumes are indexed automatically,\n"
"                so that you can easily search through their content."
msgstr ""
"Ansøgere og deres vedhæftede CV oprettes automatisk når en email afsendes.\n"
"                  Hvis du installere dokument administrations modulerne, vil alle resumeer indekseres automatisk, så du nemt kan gennemsøge deres indhold."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Applicants and their attached CV are created automatically when an email is sent.\n"
"            If you install the document management modules, all resumes are indexed automatically,\n"
"            so that you can easily search through their content."
msgstr ""
"Ansøgere og deres vedhæftede CV oprettes automatisk når en email afsendes.\n"
"Hvis du installere dokument administrations modulerne, vil alle resumeer indekseres automatisk, så du nemt kan gennemsøge deres indhold."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid ""
"Applicants can send resume to this email address,<br/>it will create an "
"application automatically"
msgstr ""
"Ansøgere kan sende resumeer til denne email adresse, <br/> det vil "
"automatisk oprette en ansøgning."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Application"
msgstr "App"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_count
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_count
msgid "Application Count"
msgstr "Antal ansøgere"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__application_status
msgid "Application Status"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Application Summary"
msgstr "Ansøgnings sammendrag"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Application email"
msgstr "Ansøger email"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_applications
#: model:ir.actions.act_window,name:hr_recruitment.crm_case_categ0_act_job
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__applicant_ids
#: model:ir.ui.menu,name:hr_recruitment.menu_crm_case_categ0_act_job
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_applications
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_view_tree_inherit
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Applications"
msgstr "Applikation"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__application_count
msgid "Applications with the same email or phone or mobile"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__job_id
msgid "Applied Job"
msgstr "Anvendt stilling"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__priority
msgid "Appreciation"
msgstr "Påskønnelse"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Archive"
msgstr "Arkivér"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Archived"
msgstr "Arkiveret"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_open
msgid "Assigned"
msgstr "Tildelt"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_attachment_count
msgid "Attachment Count"
msgstr "Antal vedhæftninger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Attachments"
msgstr "Vedhæftede filer"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Attachments, like resumes, get indexed automatically."
msgstr "Vedhæftelser, så som CV'er, indekseres automatisk."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__author_id
msgid "Author"
msgstr "Forfatter"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__availability
msgid "Availability"
msgstr "Til rådighed"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bachelor
msgid "Bachelor Degree"
msgstr "Bachelor grad"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_blocked:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Blocked"
msgstr "Blokeret"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_job_position_interviewer
msgid "By Job Positions"
msgstr "Per jobstillinger"

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid ""
"By setting an alias to a job position, emails sent to this address create "
"applications automatically. You can even use multiple trackers to get "
"statistics according to the source of the application: LinkedIn, Monster, "
"Indeed, etc."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "CV Digitization (OCR)"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "CV Display"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_calendar_event
msgid "Calendar Event"
msgstr "Kalender begivenhed"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__campaign_id
msgid "Campaign"
msgstr "Kampagne"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__can_edit_body
msgid "Can Edit Body"
msgstr "Kan Redigere Krop"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
msgid "Cancel"
msgstr "Annullér"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_graph_view_job
msgid "Cases By Stage and Estimates"
msgstr "Sager efter status og estimat"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_category
msgid "Category of applicant"
msgstr "Ansøger kategori"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Choose an application email."
msgstr "Vælg en ansøgnings email."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__color
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__color
msgid "Color Index"
msgstr "Farve index"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__company_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Company"
msgstr "Virksomhed"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_config_settings
msgid "Config Settings"
msgstr "Konfigurer opsætning"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_configuration
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Configuration"
msgstr "Konfiguration"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_congratulations
msgid "Confirmation email sent to all new job applications"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_id
#, python-format
msgid "Contact"
msgstr "Kontakt"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Contact Email"
msgstr "Kontakt e-mail"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_view_search_inherit_hr_recruitment
msgid "Content"
msgstr "Indhold"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__body
msgid "Contents"
msgstr "Indhold"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Contract"
msgstr "Kontrakt"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job4
msgid "Contract Proposal"
msgstr "Kontraktforslag"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job5
msgid "Contract Signed"
msgstr "Kontrakt underskrevet"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Copy this email address, to paste it in your email composer, to apply."
msgstr ""
"Kopier denne email adresse, for at indsætte den i din email opretter, for at"
" ansøge."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create"
msgstr "Opret"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Create Employee"
msgstr "Opret medarbejder"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.create_job_simple
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Create a Job Position"
msgstr "Opret en stilling"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Create your first Job Position."
msgstr "Opret din første Stilling."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_uid
msgid "Created by"
msgstr "Oprettet af"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__create_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__create_date
msgid "Created on"
msgstr "Oprettet den"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__create_date
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Creation Date"
msgstr "Oprettelsesdato"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Tilpasset Ikke modtaget Besked"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_close
msgid "Days to Close"
msgstr "Dage til at lukke"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__day_open
msgid "Days to Open"
msgstr "Dage til at starte op"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_defaults
msgid "Default Values"
msgstr "Standardværdier"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid ""
"Define a specific contact address for this job position. If you keep it "
"empty, the default email address will be used which is in human resources "
"settings"
msgstr ""
"Definer en specifik kontakt adresse for denne stilling. Hvis du lader den "
"stå tom, vil standard email adressen blive brugt, som kan findes i HR "
"indstillinger"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_job_stage_act
msgid ""
"Define here your stages of the recruitment process, for example:\n"
"            qualification call, first interview, second interview, refused,\n"
"            hired."
msgstr ""
"Definer stadierne for din ansættelses proces her, for eksempel:\n"
"           kvalificerings opkald, første samtale, anden samtale, afvist,\n"
"           ansat."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_degree_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__type_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_degree_tree
msgid "Degree"
msgstr "Grad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__name
msgid "Degree Name"
msgstr "Uddannelsesgrad navn"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_degree
msgid "Degrees"
msgstr "Grader"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__delay_close
msgid "Delay to Close"
msgstr "Udsæt lukning"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Delete"
msgstr "Slet"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_department
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__department_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Department"
msgstr "Afdeling"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__manager_id
msgid "Department Manager"
msgstr "Afdelingsleder"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_department
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_department
msgid "Departments"
msgstr "Afdelinger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__description
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__name
msgid "Description"
msgstr "Beskrivelse"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_digest_digest
msgid "Digest"
msgstr "Opsummering"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Digitize your CV to extract name and email automatically."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "Discard"
msgstr "Kassér"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_applicant_cv_display
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Display CV on application form"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__display_name
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__display_name
msgid "Display Name"
msgstr "Vis navn"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/digest.py:0
#, python-format
msgid "Do not have access, skip this data for user's digest email"
msgstr ""
"Har ikke adgang, spring over disse data til brugerens opsamlings e-mail"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_bac5
msgid "Doctoral Degree"
msgstr "Doktorgrad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__documents_count
msgid "Document Count"
msgstr "Dokument antal"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Documentation"
msgstr "Dokumentation"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_job.py:0
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__document_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
#, python-format
msgid "Documents"
msgstr "Dokumenter"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_1
msgid "Doesn't fit the job requirements"
msgstr "Opfylder ikke stillings kravene"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_recruitment_stage_act
msgid ""
"Don't forget to specify the department if your recruitment process\n"
"            is different according to the job position."
msgstr ""
"Glem ikke at specificere afdelingen, hvis din ansættelses process\n"
"             er forskellig afhængig af stillingen."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Dropdown menu"
msgstr "Dropdown menu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_from
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__email
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Email"
msgstr "E-mail"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Email Alias"
msgstr "E-mail alias"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__template_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__template_id
msgid "Email Template"
msgstr "E-mail skabelon"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_id
msgid ""
"Email alias for this job position. New emails will automatically create new "
"applicants for this job position."
msgstr ""
"Email alias for dette job opslag. Nye e-mails vil automatisk oprette nye "
"ansøgninger for dette job opslag."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__email_cc
msgid "Email cc"
msgstr "Email cc"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid "Email of the applicant is not set, email won't be sent."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__name
msgid "Email subject for applications sent via email"
msgstr "Email emne for ansøgninger afsendt via email"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid "Email template must be selected to send a mail"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model,name:hr_recruitment.model_hr_employee
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__emp_id
#, python-format
msgid "Employee"
msgstr "Medarbejder"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__employee_name
msgid "Employee Name"
msgstr "Medarbejder navn"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__emp_id
msgid "Employee linked to the applicant."
msgstr "Medarbejder tilknyttet denne ansøgning"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_hired_template
msgid "Employee:"
msgstr "Medarbejder:"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_employees
msgid "Employees"
msgstr "Ansatte"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_contract_type
msgid "Employment Types"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__3
msgid "Excellent"
msgstr "Fremragende"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__expected_employee
msgid "Expected Employee"
msgstr "Forventet medarbejder"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected
msgid "Expected Salary"
msgstr "Forventet løn"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Expected Salary Extra"
msgstr "Forventet ekstra løn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Extended Filters"
msgstr "Udvidede filtre"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__extended_interviewer_ids
msgid "Extended Interviewer"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Extra advantages..."
msgstr "Ekstra fordele..."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__favorite_user_ids
msgid "Favorite User"
msgstr "Favorit bruger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.ir_attachment_hr_recruitment_list_view
msgid "File"
msgstr "Fil"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job2
msgid "First Interview"
msgstr "Første samtale"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__fold
msgid "Folded in Kanban"
msgstr "Foldet i Kanban"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_follower_ids
msgid "Followers"
msgstr "Følgere"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_partner_ids
msgid "Followers (Partners)"
msgstr "Følgere (partnere)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Skrifttype awesome icon f.eks. fa-opgaver"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Future Activities"
msgstr "Fremtidige aktiviteter"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_kanban
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Generate Email"
msgstr "Generer Email"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_get_refuse_reason
msgid "Get Refuse Reason"
msgstr "Få Afvisningsgrundlag"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__1
msgid "Good"
msgstr "Godt"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_graduate
msgid "Graduate"
msgstr "Bestå"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__done
msgid "Green"
msgstr "Grøn"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_done
msgid "Green Kanban Label"
msgstr "Grøn kanban label"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__normal
msgid "Grey"
msgstr "Grå"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_normal
msgid "Grey Kanban Label"
msgstr "Grå kanban label"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__group_applicant_cv_display
msgid "Group Applicant Cv Display"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Group By"
msgstr "Sortér efter"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__hr_responsible_id
msgid "HR Responsible"
msgstr "HR ansvarlig"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__has_domain
msgid "Has Domain"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__has_message
msgid "Has Message"
msgstr "Har besked"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_closed
msgid "Hire Date"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__hired
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Hired"
msgstr "Ansat"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid "Hired Stage"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__id
msgid "ID"
msgstr "ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID for overordnet record indeholdende aliaset (eksempel: projekt, der "
"indeholder opgaveoprettelses aliaset)"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_exception_icon
msgid "Icon"
msgstr "Ikon"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikon for uventet aktivitet."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Hvis afkrydset, kræver nye beskeder din opmærksomhed "

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Hvis afkrydset har nogle beskeder en leveringsfejl"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__hired_stage
msgid ""
"If checked, this stage is used to determine the hire date of an applicant"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__template_id
msgid ""
"If set, a message is posted on the applicant using the template when the "
"applicant is set to the stage."
msgstr ""
"Hvis angivet, vil der blive posteret en besked ved ansøgeren der bruger "
"skabelonen, når ansøgeren er angivet til stadiet."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Hvis angivet vil dette indhold automatisk blive sendt ud til uautoriserede "
"brugere, i stedet for standard beskeden."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__active
msgid ""
"If the active field is set to false, it will allow you to hide the case "
"without removing it."
msgstr ""
"Hvis det aktive felt er angivet til Falsk, vil det gøre dig i stand til at "
"skjule sagen uden at fjerne den."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_normal:hr_recruitment.stage_job5
#, python-format
msgid "In Progress"
msgstr "I gang"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job1
msgid "Initial Qualification"
msgstr "Indledende kvalifikation"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_survey
msgid "Interview Forms"
msgstr "Samtaleskemaer"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__interviewer_ids
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__interviewer_ids
msgid "Interviewers"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__is_mail_template_editor
msgid "Is Editor"
msgstr "Er Redaktør"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__is_favorite
msgid "Is Favorite"
msgstr "Er favorit"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_is_follower
msgid "Is Follower"
msgstr "Er følger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__is_warning_visible
msgid "Is Warning Visible"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_job.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__job_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Job"
msgstr "Job"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__application_ids
#: model_terms:ir.ui.view,arch_db:hr_recruitment.crm_case_pivot_view_job
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
#, python-format
msgid "Job Applications"
msgstr "Jobansøgninger"

#. module: hr_recruitment
#: model:utm.campaign,title:hr_recruitment.utm_campaign_job
msgid "Job Campaign"
msgstr "Jobkampagne"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__address_id
msgid "Job Location"
msgstr "Joblokation"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_job
msgid "Job Position"
msgstr "Stilling"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_department_new
msgid "Job Position Created"
msgstr "Stilling oprettet"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_job_new
msgid "Job Position created"
msgstr "Stilling oprettet"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_config
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_interviewer
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_config_jobs
msgid "Job Positions"
msgstr "Stillinger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Job Posting"
msgstr "Stilling"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid "Job Specific"
msgstr "Job specifik"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Jobs"
msgstr "Job"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Jobs - Recruitment Form"
msgstr "Stillinger - rekrutteringsformular"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_sources
msgid "Jobs Sources"
msgstr "Job kilder"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_blocked
msgid "Kanban Blocked"
msgstr "Tavle blokeret"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_normal
msgid "Kanban Ongoing"
msgstr "Tavle igangværende"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__kanban_state
msgid "Kanban State"
msgstr "Kanban status"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__legend_done
msgid "Kanban Valid"
msgstr "Tavle gyldig"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues_value
msgid "Kpi Hr Recruitment New Colleagues Value"
msgstr "KPI HR Ansættelse Ny kollega Værdi"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__lang
msgid "Language"
msgstr "Sprog"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Last Meeting"
msgstr "Seneste møde"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source____last_update
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage____last_update
msgid "Last Modified on"
msgstr "Sidst ændret den"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__last_stage_id
msgid "Last Stage"
msgstr "Sidste fase"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__date_last_stage_update
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Last Stage Update"
msgstr "Sidste faseopdatering"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_uid
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_uid
msgid "Last Updated by"
msgstr "Sidst opdateret af"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_refuse_reason__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__write_date
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__write_date
msgid "Last Updated on"
msgstr "Sidst opdateret den"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Late Activities"
msgstr "Overskredet aktiviteter"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Let people apply by email to save time."
msgstr "Lad folk ansøge via email for at spare tid."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Let's create a job position."
msgstr "Lad os oprette en stilling."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid ""
"Let's create the position. An email will be setup for applications, and a "
"public job description, if you use the Website app."
msgstr ""
"Lad os oprette en stilling. En email vil blive opsat til ansøgninger, samt "
"en offentlig jobbeskrivelse, hvis du bruger Hjemmeside applikationen."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let's have a look at how to <b>improve</b> your <b>hiring process</b>."
msgstr ""
"Lad os tage et kig på hvordan du kan <b>forbedre</b> din <b>ansættelses "
"process</b>."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let's have a look at the applications pipeline."
msgstr "Lad os tage et kig på ansøgnings pipelinen."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let’s create this new employee now."
msgstr "Lad os oprette denne nye medarbejder nu."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Let’s go back to the dashboard."
msgstr "Lad os gå tilbage til instrumentbrættet."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__linkedin_profile
msgid "LinkedIn Profile"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__template_id
msgid "Mail Template"
msgstr "Mailskabelon"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_main_attachment_id
msgid "Main Attachment"
msgstr "Vedhæftning"

#. module: hr_recruitment
#: model:hr.recruitment.degree,name:hr_recruitment.degree_licenced
msgid "Master Degree"
msgstr "Kandidatgrad"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__medium_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__medium_id
msgid "Medium"
msgstr "Medium"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_mediums
msgid "Mediums"
msgstr "Medier"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_date
msgid "Meeting Display Date"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_display_text
msgid "Meeting Display Text"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__meeting_ids
msgid "Meetings"
msgstr "Møder"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_ir_ui_menu
msgid "Menu"
msgstr "Menu"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error
msgid "Message Delivery error"
msgstr "Besked ved leveringsfejl"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_ids
msgid "Messages"
msgstr "Beskeder"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_mobile
msgid "Mobile"
msgstr "Mobil"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Motivations..."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mine Aktiviteter Deadline"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "My Applications"
msgstr "Mine ansøgninger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_job_filter_recruitment
msgid "My Favorites"
msgstr "Mine favoritter"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_search_view
msgid "My Job Positions"
msgstr "Mine stillinger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__name
msgid "Name"
msgstr "Navn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "New"
msgstr "Ny"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_applicant_count
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_new
#: model:mail.message.subtype,name:hr_recruitment.mt_job_applicant_new
msgid "New Applicant"
msgstr "Ny ansøger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "New Applicants"
msgstr "Nye ansøgere"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_job_new_application
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__new_application_count
msgid "New Application"
msgstr "Ny ansøgning"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_from_department
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "New Applications"
msgstr "Nye ansøgninger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_digest_digest__kpi_hr_recruitment_new_colleagues
msgid "New Employees"
msgstr "Nye ansatte"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_department__new_hired_employee
msgid "New Hired Employee"
msgstr "Nyansat medarbejder"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_employee_view_search
msgid "Newly Hired"
msgstr "Nyligt ansat"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_employee_action_from_department
msgid "Newly Hired Employees"
msgstr "Nyansatte medarbejdere"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_employee__newly_hired_employee
msgid "Newly hired employee"
msgstr "Nyansat medarbejder"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_tree_activity
msgid "Next Activities"
msgstr "Næste aktiviteter"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Næste Aktivitet Kalender Arrangement"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Deadline for næste aktivitet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_summary
msgid "Next Activity Summary"
msgstr "Oversigt over næste aktivitet"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_type_id
msgid "Next Activity Type"
msgstr "Næste aktivitetstype"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "Next Meeting"
msgstr "Næste møde"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No Meeting"
msgstr "Intet møde"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No Subject"
msgstr "Intet emne"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "No application yet"
msgstr "Ingen ansøgninger endnu"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid "No applications yet"
msgstr "Ingen ansøgere endnu"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.hr_applicant_action_analysis
msgid "No data yet!"
msgstr "Ingen data endnu!"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__0
msgid "Normal"
msgstr "Normal"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of Actions"
msgstr "Antal handlinger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__attachment_number
msgid "Number of Attachments"
msgstr "Antal vedhæftninger"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__new_application_count
msgid ""
"Number of applications that are new in the flow (typically at first step of "
"the flow)"
msgstr ""
"Antal ansøgninger der er nye i flowet (typisk på første trin i flowet)"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__delay_close
msgid "Number of days to close"
msgstr "Antal dage til afsluttelse"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of errors"
msgstr "Antal fejl"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr "Antal meddelser der kræver handling"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Antal beskeder med leveringsfejl"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_applications
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"                process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo hjælper dig med at følge ansøgere i ansættelses processen, samt med "
"opfølgninger af alle handlinger: møder, samtaler, osv."

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.crm_case_categ0_act_job
msgid ""
"Odoo helps you track applicants in the recruitment\n"
"            process and follow up all operations: meetings, interviews, etc."
msgstr ""
"Odoo hjælper dig med at følge ansøgere i ansættelses\n"
"            processen, samt med opfølgning af alle handlinger: møder, samtaler, osv."

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_user
msgid "Officer : Manage all applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__old_application_count
msgid "Old Application"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__ongoing
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Ongoing"
msgstr "Igangværende"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_website_hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Online Posting"
msgstr "Online opslag"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Valgfri ID for en tråd (record), som alle indgående meddelelser vil blive "
"vedhæftet, selvom de ikke svarede på det. Hvis opsat, vil dette fuldstændigt"
" deaktivere oprettelsen af nye poster."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_applicant_send_mail__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"Valgfri oversættelses sprog (ISO kode) til valg ved afsending af en email. "
"Hvis ikke angivet, vil den engelske udgave blive anvendt. Dette bør "
"normaltvis være et pladsholder-udtryk der angiver det passende sprog, f.eks."
" {{ object.partner_id.lang }}."

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Or talk about this applicant privately with your colleagues."
msgstr "Eller snak om denne ansøger privat med dine kolleger."

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Other applications"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_user_id
msgid "Owner"
msgstr "Ejer"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_model_id
msgid "Parent Model"
msgstr "Overordnet model"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Overordnet tråd (record) ID"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Overordnet model der holder aliaset. Modellen med aliasreferencen er ikke "
"nødvendigvis den model, der er givet af alias_model_id (eksempel: projekt "
"(parent_model) og opgave (model))."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__hr_responsible_id
msgid "Person responsible of validating the employee's contracts."
msgstr "Person som er ansvarlig for at bekræfte medarbejderens kontrakter."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__partner_phone
msgid "Phone"
msgstr "Telefon"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Politik til at oprette en besked på dokumentet ved hjælp af mailgatewayen.\n"
"- alle: alle kan skrive\n"
"- partnere: kun godkendte partnere\n"
"- følgere: kun følgere af det relaterede dokument eller medlemmer af følgende kanaler\n"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__probability
msgid "Probability"
msgstr "Sandsynlighed"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Proposed Salary"
msgstr "Foreslået løn"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Proposed Salary Extra"
msgstr "Foreslået løn ekstra"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Publish available jobs on your website"
msgstr "Udgiv ledige stillinger på din hjemmeside"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job1
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job2
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job3
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job4
#: model:hr.recruitment.stage,legend_done:hr_recruitment.stage_job5
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Ready for Next Stage"
msgstr "Klar til næste fase"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_config
msgid "Ready to recruit more efficiently?"
msgstr "Klar til at rekruttere mere effektivt?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Optag tråd ID"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_job__user_id
msgid "Recruiter"
msgstr "Ansætter"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_root
#: model_terms:ir.ui.view,arch_db:hr_recruitment.digest_digest_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment"
msgstr "Ansættelse"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_job_stage_act
msgid "Recruitment / Applicants Stages"
msgstr "Rekruttering / ansøgningstrin"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_department
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_report_filtered_job
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_action_analysis
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_graph
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_pivot
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
msgid "Recruitment Analysis"
msgstr "Ansættelsesanalyse"

#. module: hr_recruitment
#: model:res.groups,name:hr_recruitment.group_hr_recruitment_interviewer
msgid "Recruitment Interviewer"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Recruitment Process"
msgstr "Ansættelsesproces"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_stage
msgid "Recruitment Stages"
msgstr "Ansættelses stadier"

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_congratulations
msgid "Recruitment: Application Acknowledgement"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_interest
msgid "Recruitment: Interest"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_not_interested
msgid "Recruitment: Not interested anymore"
msgstr ""

#. module: hr_recruitment
#: model:mail.template,name:hr_recruitment.email_template_data_applicant_refuse
msgid "Recruitment: Refuse"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_department_view_kanban
msgid "Recruitments"
msgstr "Ansættelser"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__kanban_state__blocked
msgid "Red"
msgstr "Rød"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__legend_blocked
msgid "Red Kanban Label"
msgstr "Rød kanban label"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Refuse"
msgstr "Afslå"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.actions.act_window,name:hr_recruitment.applicant_get_refuse_reason_action
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__refuse_reason_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__refuse_reason_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_refuse_reason_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#, python-format
msgid "Refuse Reason"
msgstr "Afslagsgrundlag"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_applicant_refuse_reason
msgid "Refuse Reason of Applicant"
msgstr "Afslagsgrundlag for Ansøger"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_refuse_reason_action
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_applicant_refuse_reason
msgid "Refuse Reasons"
msgstr "Afslagsgrundlag"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__application_status__refused
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Refused"
msgstr "Afvist"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_survey
msgid "Remote"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__render_model
msgid "Rendering Model"
msgstr "Gengivelsesmodel"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.report_hr_recruitment
msgid "Reporting"
msgstr "Rapportering"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__requirements
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Requirements"
msgstr "Krav"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Reserve"
msgstr "Reserver"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Responsible"
msgstr "Ansvarlig"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__activity_user_id
msgid "Responsible User"
msgstr "Ansvarlig bruger"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "Restore"
msgstr "Gendan"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Running Applicants"
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS leveringsfejl"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected
msgid "Salary Expected by Applicant"
msgstr "Ansøgerens forventede løn"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_expected_extra
msgid "Salary Expected by Applicant, extra advantages"
msgstr "Løn forventet af ansøger, ekstra fordele"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed
msgid "Salary Proposed by the Organisation"
msgstr "Løn foreslået af organisationen"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__salary_proposed_extra
msgid "Salary Proposed by the Organisation, extra advantages"
msgstr "Løn foreslået af organisationen, ekstra fordele"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Save it !"
msgstr "Gem det !"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_kanban_view_applicant
msgid "Schedule Interview"
msgstr "Planlæg samtale"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Search Applicants"
msgstr "Søg ansøgerer"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_view_search
msgid "Search Source"
msgstr "Søg Kilde"

#. module: hr_recruitment
#: model:hr.recruitment.stage,name:hr_recruitment.stage_job3
msgid "Second Interview"
msgstr "Anden samtale"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
msgid "Send"
msgstr "Send"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_res_config_settings__module_hr_recruitment_extract
msgid "Send CV to OCR to fill applications"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#: model:ir.actions.server,name:hr_recruitment.action_applicant_send_mail
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_get_refuse_reason__send_mail
#, python-format
msgid "Send Email"
msgstr "Send e-mail"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send Interview Survey"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send SMS"
msgstr "Send SMS"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Send an Interview Survey to the applicant during\n"
"                                        the recruitment process"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_applicant_send_mail
msgid "Send mails to applicants"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Send texts to your contacts"
msgstr "Send SMS beskeder til dine kontakter"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "Send your email. Followers will get a copy of the communication."
msgstr "Send din email. Følgere vil få en kopi af kommunikationen."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_degree__sequence
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__sequence
msgid "Sequence"
msgstr "Sekvens"

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_interest
msgid ""
"Set this template to a recruitment stage to send it when applications reach "
"that stage"
msgstr ""

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.action_hr_recruitment_configuration
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_global_settings
msgid "Settings"
msgstr "Opsætning"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Show all records which has next action date is before today"
msgstr "Vis alle poster, hvor den næste aktivitetsdato er før i dag"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__source_id
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_source__source_id
msgid "Source"
msgstr "Kilde"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_source_menu
msgid "Source Analysis"
msgstr ""

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_hr_recruitment_source
msgid "Source of Applicants"
msgstr "Ansøger kilde"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm_sources
msgid "Sources"
msgstr "Kilder"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "Sources of Applicants"
msgstr "Ansøgerer kilde"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__job_ids
msgid ""
"Specific jobs that uses this stage. Other jobs will not use this stage."
msgstr ""
"Specifikke jobs der bruger dette stadie. Andre job vil ikke bruge dette "
"stadie."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__stage_id
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage"
msgstr "Fase"

#. module: hr_recruitment
#: model:mail.message.subtype,name:hr_recruitment.mt_applicant_stage_changed
msgid "Stage Changed"
msgstr "Fase ændret"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Stage Definition"
msgstr "Stadie definition"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_recruitment_stage__name
msgid "Stage Name"
msgstr "Fase navn"

#. module: hr_recruitment
#: model:mail.message.subtype,description:hr_recruitment.mt_applicant_stage_changed
msgid "Stage changed"
msgstr "Fase ændret"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__last_stage_id
msgid ""
"Stage of the applicant before being in the current stage. Used for lost "
"cases analysis."
msgstr ""
"Stadie på ansøgeren før de var i nuværende stadie. Brugt til tabt sags "
"analyse."

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_recruitment_stage_act
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_stage
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_tree
msgid "Stages"
msgstr "Faser"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status baseret på aktiviteter\n"
"Forfaldne: Forfaldsdato er allerede overskredet\n"
"I dag: Aktivitetsdato er i dag\n"
"Planlagt: Fremtidige aktiviteter."

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_applicant_send_mail__subject
msgid "Subject"
msgstr "Emne"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__name
msgid "Subject / Application"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_get_refuse_reason_view_form
msgid "Submit"
msgstr "Indsend"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant_category__name
msgid "Tag Name"
msgstr "Tag-navn"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_applicant_category_name_uniq
msgid "Tag name already exists !"
msgstr "Tag navn eksisterer allerede!"

#. module: hr_recruitment
#: model:ir.actions.act_window,name:hr_recruitment.hr_applicant_category_action
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__categ_ids
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_category_menu
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_category_view_tree
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Tags"
msgstr "Tags"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_report_team_menu
msgid "Team Performance"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_campaign.py:0
#, python-format
msgid ""
"The UTM campaign '%s' cannot be deleted as it is used in the recruitment "
"process."
msgstr ""

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_3
msgid "The applicant gets a better offer"
msgstr "Ansøgeren får et bedre tilbud"

#. module: hr_recruitment
#: model:hr.applicant.refuse.reason,name:hr_recruitment.refuse_reason_2
msgid "The applicant is not interested anymore"
msgstr "Ansøgeren er ikke længere interesseret"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__availability
msgid "The date at which the applicant will be available to start working"
msgstr "Dato hvorfra ansøger kan påbegynde arbejde"

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_refuse_reason.py:0
#, python-format
msgid ""
"The email will not be sent to the following applicant(s) as they don't have "
"email address."
msgstr ""
"Denne email vil ikke blive sendt til følgende ansøger(e) da de ikke har "
"nogen emailadresser."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/wizard/applicant_send_mail.py:0
#, python-format
msgid "The following applicants are missing an email address: %s."
msgstr ""

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Modellen (Odoo Document Kind), som dette alias svarer til. Enhver indgående "
"e-mail, der ikke svarer til en eksisterende post, vil medføre oprettelse af "
"en ny rekord af denne model (fx en projektopgave)"

#. module: hr_recruitment
#: model:ir.model.constraint,message:hr_recruitment.constraint_hr_recruitment_degree_name_uniq
msgid "The name of the Degree of Recruitment must be unique!"
msgstr "Navnet på ansættelsesforholdet skal være unikt!"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Navnet på e-mail aliaset, f.eks. 'jobs', hvis du vil fange e-mails til "
"<<EMAIL>>"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_job__alias_user_id
msgid ""
"The owner of records created upon receiving emails on this alias. If this "
"field is not set the system will attempt to find the right owner based on "
"the sender (From) address, or will use the Administrator account if no "
"system user is found for that address."
msgstr ""
"Ejeren af poster oprettet ved modtagelse af e-mails på dette alias. Hvis "
"dette felt ikke er indstillet, forsøger systemet at finde den rigtige ejer "
"baseret på afsenderens (Fra) adresse eller bruger administratorkontoen, hvis"
" der ikke findes en systembruger for den pågældende adresse."

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__campaign_id
msgid ""
"This is a name that helps you keep track of your different campaign efforts,"
" e.g. Fall_Drive, Christmas_Special"
msgstr ""
"Dette er et navn, der hjælper dig med at holde styr på dine forskellige "
"kampagneindsatser"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__medium_id
msgid "This is the method of delivery, e.g. Postcard, Email, or Banner Ad"
msgstr "Dette er leveringsmetoden. Fx postkort, e-mail eller bannerannonce"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__source_id
msgid ""
"This is the source of the link, e.g. Search Engine, another domain, or name "
"of email list"
msgstr ""
"Dette er kilden til linket, fx søgemaskine, andet domæne eller navn på "
"mailliste"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_recruitment_stage__fold
msgid ""
"This stage is folded in the kanban view when there are no records in that "
"stage to display."
msgstr ""
"Denne fase er foldet i kanbanvisningen, når der ikke er nogen journaler at "
"vise i fasen."

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.hr_applicant_stage_report_menu
msgid "Time In Stage Analysis"
msgstr ""

#. module: hr_recruitment
#: model:digest.tip,name:hr_recruitment.digest_tip_hr_recruitment_0
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Tip: Let candidates apply by email"
msgstr "Råd: Lad kandidater ansøge via email"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "To Recruit"
msgstr "At ansætte"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Today Activities"
msgstr "Dagens aktiviteter"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid "Tooltips"
msgstr "Værktøjstip"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Trackers"
msgstr ""

#. module: hr_recruitment
#: model_terms:digest.tip,tip_description:hr_recruitment.digest_tip_hr_recruitment_0
msgid "Try sending an email"
msgstr "Prøv at sende en email"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Type af undtagelsesaktivitet registreret "

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_campaign
msgid "UTM Campaign"
msgstr "UTM Kampagne"

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_utm_source
msgid "UTM Source"
msgstr "UTM kilde"

#. module: hr_recruitment
#: model:ir.ui.menu,name:hr_recruitment.menu_hr_recruitment_utm
msgid "UTMs"
msgstr "UTM'er"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.view_hr_job_kanban
msgid "Unarchive"
msgstr "Aktivér"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unassigned"
msgstr "Ikke tildelt"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_search_bis
msgid "Unread Messages"
msgstr "Ulæste beskeder"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid "Use OCR to fill data from a picture of the CV or the file itself"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Use emails and links trackers"
msgstr "Anvend emails og link sporer"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.res_config_settings_view_form
msgid ""
"Use interview forms tailored to each job position during the recruitment "
"process. Select the form to use in the job position detail form. This relies"
" on the Survey app."
msgstr ""
"Brug interview formularer skræddersyet til hver enkelt stilling under "
"ansættelses processen. Vælg formularen der skal bruges i stillingens detalje"
" formular. Dette afhænger af Undersøgelse applikationen."

#. module: hr_recruitment
#: model:ir.model,name:hr_recruitment.model_res_users
msgid "User"
msgstr "Bruger"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__user_email
msgid "User Email"
msgstr "Bruger e-mail"

#. module: hr_recruitment
#: model:ir.model.fields.selection,name:hr_recruitment.selection__hr_applicant__priority__2
msgid "Very Good"
msgstr "Meget godt"

#. module: hr_recruitment
#: model_terms:ir.actions.act_window,help:hr_recruitment.action_hr_job_sources
msgid "Want to analyse where applications come from ?"
msgstr "Vil du analysere hvor ansøgninger kommer fra ?"

#. module: hr_recruitment
#: model:ir.model.fields,field_description:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website Messages"
msgstr "Beskeder fra hjemmesiden"

#. module: hr_recruitment
#: model:ir.model.fields,help:hr_recruitment.field_hr_applicant__website_message_ids
msgid "Website communication history"
msgstr "Website kommunikations historik"

#. module: hr_recruitment
#. odoo-javascript
#: code:addons/hr_recruitment/static/src/js/tours/hr_recruitment.js:0
#, python-format
msgid "What do you want to recruit today? Choose a job title..."
msgstr "Hvad vil du ansætte i dag? Vælg en job titel..."

#. module: hr_recruitment
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,description:hr_recruitment.email_template_data_applicant_refuse
msgid "When you refuse an application, you can choose this template"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.applicant_send_mail_view_form
msgid "Write your message here..."
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "You are not allowed to perform this action."
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_stage_form
msgid ""
"You can define here the labels that will be displayed for the kanban state instead\n"
"                        of the default labels."
msgstr ""
"Du kan her definere mærkaterne som vil blive vist i tavle tilstanden, i stedet for \n"
"                        standard mærkaterne."

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/utm_source.py:0
#, python-format
msgid ""
"You cannot delete these UTM Sources as they are linked to the following recruitment sources in Recruitment:\n"
"%(recruitment_sources)s"
msgstr ""

#. module: hr_recruitment
#. odoo-python
#: code:addons/hr_recruitment/models/hr_recruitment.py:0
#, python-format
msgid "You must define a Contact Name for this applicant."
msgstr "Du skal definere et kontaktnavn for denne ansøger."

#. module: hr_recruitment
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_congratulations
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_interest
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_not_interested
#: model:mail.template,subject:hr_recruitment.email_template_data_applicant_refuse
msgid "Your Job Application: {{ object.job_id.name }}"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_recruitment_source_tree
msgid "e.g. LinkedIn"
msgstr "f.eks. LinkedIn"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. Sales Manager"
msgstr "fx salgschef"

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_applicant_view_form
msgid "e.g. Sales Manager 2 year experience"
msgstr ""

#. module: hr_recruitment
#: model_terms:ir.ui.view,arch_db:hr_recruitment.hr_job_simple_form
msgid "e.g. sales-manager"
msgstr "f.eks. salgs-leder"
