<odoo>
    <record id="approval_request_product_view_tree" model="ir.ui.view">
        <field name="name">approval.request.view.product.tree</field>
        <field name="model">approval.request</field>
        <field name="priority">10</field>
        <field name="arch" type="xml">
            <tree sample="1" decoration-info="request_status == 'new'">
                <field name="name"/>
                <field name="request_owner_id" widget="many2one_avatar_user"/>
                <field name="category_id"/>
                <field name="activity_ids" widget="list_activity"/>
                <field name="request_status" decoration-info="request_status == 'new'" decoration-warning="request_status == 'pending'" decoration-success="request_status == 'approved'" decoration-danger="request_status == 'refused'" widget="badge"/>
            </tree>
        </field>
    </record>
   <record id="action_approve_product" model="ir.actions.server">
        <field name="name"><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> sản phẩm</field>
        <field name="model_id" ref="approvals.model_approval_category"/>
           <field name="state">code</field>
        <field name="code">
           action = model.create_request_product()
        </field>
    </record>
</odoo>