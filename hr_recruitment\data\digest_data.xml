<?xml version='1.0' encoding='utf-8'?>
<odoo>
    <data noupdate="1">
        <record id="digest.digest_digest_default" model="digest.digest">
            <field name="kpi_hr_recruitment_new_colleagues">True</field>
        </record>
    </data>

    <data>
        <record id="digest_tip_hr_recruitment_0" model="digest.tip">
            <field name="name">Tip: Let candidates apply by email</field>
            <field name="sequence">1300</field>
            <field name="group_id" ref="hr_recruitment.group_hr_recruitment_manager" />
            <field name="tip_description" type="html">
<div>
    <p class="tip_title">Tip: Let candidates apply by email</p>
    <p class="tip_content">
        By setting an alias to a job position, emails sent to this address create applications automatically. You can even use multiple trackers to get statistics according to the source of the application: LinkedIn, Monster, Indeed, etc.
        <t t-set="record" t-value="object.env['hr.job'].search([('alias_name', '!=', False)], limit=1)" />
        <t t-if="record and record.alias_domain">
            <a t-attf-href="mailto:{{record.alias_id.display_name}}" target="_blank" style="color: #875a7b; text-decoration: none;">Try sending an email</a>
        </t>
    </p>
</div>
            </field>
        </record>
    </data>
</odoo>
