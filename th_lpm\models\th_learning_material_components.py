from odoo import models, fields,api


class LearningMaterialComponents(models.Model):
    _name = 'th.learning.material.components'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Thành phần học li<PERSON>"
    _rec_name = 'th_code'

    th_code = fields.Char(string="Mã thành phần", required=True)
    name = fields.Char(string="Tên thành phần", required=True)
    th_unit = fields.Char(string="Đơn vị tính")
    th_attribute_ids = fields.One2many("th.attribute", "th_material_components_code_id", string="Thuộc tính")

