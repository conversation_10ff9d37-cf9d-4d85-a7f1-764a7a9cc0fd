from odoo import models, fields, api
from odoo.exceptions import ValidationError


class ThArea(models.Model):
    _name = 'th.area'
    _description = 'Cấu hình khu vực'

    name = fields.Char(string='Tên khu vực')
    th_area_code = fields.Char(string='Mã khu vực', readonly=True)

    @api.model
    def default_get(self, fields_list):
        defaults = super(ThArea, self).default_get(fields_list)
        if 'th_area_code' in fields_list:
            # Tìm mã khu vực lớn nhất hiện tại
            areas = self.search([('th_area_code', 'like', 'kh%')])
            max_code = 0
            for area in areas:
                try:
                    code_num = int(area.th_area_code[2:])
                    if code_num > max_code:
                        max_code = code_num
                except:
                    continue
            
            # Tạo mã mới
            new_code = max_code + 1
            defaults['th_area_code'] = f'kh{new_code:02d}'
        return defaults

    @api.model
    def create(self, vals):
        if not vals.get('th_area_code'):
            # Tìm mã khu vực lớn nhất hiện tại
            areas = self.search([('th_area_code', 'like', 'kh%')])
            max_code = 0
            for area in areas:
                try:
                    code_num = int(area.th_area_code[2:])
                    if code_num > max_code:
                        max_code = code_num
                except:
                    continue
            
            # Tạo mã mới
            new_code = max_code + 1
            vals['th_area_code'] = f'kh{new_code:02d}'
        
        return super(ThArea, self).create(vals)


    @api.constrains('name', 'th_area_code')
    def _th_check_unique_area(self):
        for record in self:
            # Check unique name
            name_count = self.search_count([
                ('name', '=', record.name),
                ('id', '!=', record.id)
            ])
            if name_count > 0:
                raise ValidationError('Tên khu vực đã tồn tại!')

