<odoo>
    <record id="th_university_view_kanban_lpm" model="ir.ui.view">
        <field name="name">th_university_view_kanban_lpm</field>
        <field name="model">th.origin</field>
        <field name="arch" type="xml">
            <kanban  action="action_project" type="object">
                <field name="name"/>
                <field name="th_description"/>
                <field name="th_address"/>
                <field name="color"/>
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="#{!selection_mode ? kanban_color(record.color.raw_value) : ''} oe_kanban_global_click">
                            <span class="oe_kanban_color_help"
                                  t-attf-title="In #{kanban_getcolorname(record.color.raw_value)}" role="img"
                                  t-attf-aria-label="In #{kanban_getcolorname(record.color.raw_value)}"/>
                            <div t-attf-class="o_kanban_card_header">
                                <div class="o_kanban_card_header_title">
                                    <div class="o_kanban_record_title oe_kanban_details">
                                        <strong><h4><field name="name"/></h4></strong>
                                    </div>
                                    <div t-if="record.th_description">
                                        <i class="fa fa-info-circle" title="Description" role="img" aria-label="Description"></i>Description
                                        <t t-esc="record.th_description.value"/>
                                    </div>
                                </div>
                                <div class="o_kanban_manage_button_section" t-if="!selection_mode">
                                    <a class="o_kanban_manage_toggle_button" href="#">
                                        <i class="fa fa-ellipsis-v" role="img" aria-label="Manage" title="Manage"/>
                                    </a>
                                </div>
                            </div>
                            <div class="o_dropdown_kanban dropdown">
                                <a class="dropdown-toggle o-no-caret btn" role="button" data-bs-toggle="dropdown" data-bs-display="static" href="#" aria-label="Dropdown menu" title="Dropdown menu">
                                    <span class="fa fa-ellipsis-v"/>
                                </a>
                                <div class="dropdown-menu" role="menu">
                                    <t t-if="widget.editable"><a role="menuitem" type="edit" class="dropdown-item">Edit</a></t>
                                    <t t-if="widget.deletable"><a role="menuitem" type="delete" class="dropdown-item">Delete</a></t>
                                    <ul class="oe_kanban_colorpicker" data-field="color"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <record id="th_lpm_university_kanban_action" model="ir.actions.act_window">
        <field name="name">Trường học</field>
        <field name="res_model">th.origin</field>
        <field name="view_mode">kanban,tree,form</field>
        <field name="view_id" ref="th_university_view_kanban_lpm"/>
        <field name="context">{'create': False}</field>
    </record>

</odoo>