from odoo import models, fields


class ThProperties(models.Model):
    _name = 'th.attribute'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Thuộc tính thành phần học liệu"
    _rec_name = 'th_attribute_code'

    th_material_components_code_id = fields.Many2one(comodel_name='th.learning.material.components', string="Mã thành phần học liệu")
    name = fields.Char(string="Tên thuộc tính", required=True)
    th_unit = fields.Char(string="Đơn vị tính")
    th_attribute_code = fields.Char(string="<PERSON>ã thuộc tính")
    th_value_attribute_ids = fields.One2many("th.value.attribute", "th_attribute_code_id", string="Giá trị thuộc tính")
    # th_increase_time = fields.Boolean(string="<PERSON>à thuộc tính tăng thêm thời gian")






