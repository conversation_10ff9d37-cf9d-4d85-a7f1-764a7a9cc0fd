from odoo import models, fields, api
import json
from odoo.exceptions import ValidationError

class ThProperties(models.Model):
    _name = 'th.product.manufacturing'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "sản phấm sản xuất"
    _rec_name = 'th_project_template_id'

    readonly_domain = fields.Char(compute="_compute_readonly_domain")
    th_project_template_id = fields.Many2one(comodel_name='product.template',
                                                     string ="Tên học phần")
    th_default_code = fields.Char(related="th_project_template_id.default_code", string="Mã HL AUM")
    th_term_code = fields.Char(string="Mã học phần")
    th_type = fields.Selection([('theory', 'Lý thuyết'),
                                ('practice', 'Thực hành'),
                                ('project', 'Đồ án')], string="Loại")
    th_slot = fields.Integer("Slot")
    th_units_used_id = fields.Many2one(comodel_name="th.origin", string="Đơn vị sử dụng", tracking=True)
    th_major_ids = fields.Many2many('th.major', string='Ngành học')
    th_course_code = fields.Char(string='Mã học phần')
    th_number_of_credits = fields.Integer(string='Số tín chỉ')
    th_project_lpm_id = fields.Many2one('th.project.lpm', string='Dự án')
    th_hourly_cost_parameter_id = fields.Many2one('th.hourly.cost.parameter', string='Chi phí theo giờ', tracking=True)
    th_standard_costs = fields.Float(string='Chi phí theo tiêu chuẩn sản xuất',compute="_compute_standard_costs",digits=(16, 0) )
    th_proposed_costs = fields.Float(string='Chi phí đề xuất theo tín chỉ',digits=(16, 0))
    th_object_costs = fields.Float(string='Chi phí đề xuất theo môn',digits=(16, 0))
    th_signal_costs_id = fields.Many2one('th.signal.costs', string='Đơn giá theo tín chỉ', tracking=True)
    th_production_standard_id = fields.Many2one('th.production.standard',
                                                 string='Tiêu chuẩn sản xuất', tracking=True,
                                                 )

    th_subject_duration = fields.Float(string="Thời lượng môn", related="th_production_standard_id.th_subject_duration")
    th_lesson_duration = fields.Float(string="Thời lượng bài", related="th_production_standard_id.th_lesson_duration")
    th_item_duration = fields.Float(string="Thời lượng mục", related="th_production_standard_id.th_item_duration")

    th_number_of_articles = fields.Float(string="Số lượng bài")
    th_number_of_items = fields.Float(string="Số lượng mục", readonly=1)
    th_packaging_lesson_ids = fields.Many2many(comodel_name='th.packaging.standard',relation='th_packaging_lesson_ids_rel', string="Tham số bài",domain="[('is_post_parameters', '=?', True)]")
    th_packaging_section_ids = fields.Many2many(comodel_name='th.packaging.standard',relation='th_packaging_section_ids_rel', string="Tham số mục",domain="[('is_item_parameter', '=?', True)]")
    th_packaging_object_ids = fields.Many2many(comodel_name='th.production.parameters', string="Tham số môn",domain="[('is_subject_parameter', '=?', True)]", default=lambda self: [self.env.ref('th_lpm.lpm_parameter_02').id])
    th_percent_gv = fields.Float(string="%GV", default=100)
    th_production_time = fields.Float(string="Tổng thời lượng sản xuất", compute="_compute_production_time")
    th_day = fields.Float(string="Ngày")
    display_name = fields.Char()
    th_object_type = fields.Selection([('base', 'Cơ sở'),
                                       ('specialized', 'Chuyên ngành'),
                                       ('outline', 'Đại cương'),
                                       ('internship', 'Thực tập'),
                                       ('project', 'Đề án,đồ án'),
                                       ('practice', 'Thực tập - rèn luyện')], string="Phân loại môn")
    th_production_standard_domain = fields.Char(compute="_compute_th_production_standard_domain")
    th_description = fields.Text(string="Ghi chú")
    th_language = fields.Selection([('vietnamese', 'Tiếng Việt'),
                                    ('english', 'Tiếng Anh'), ], string="Ngôn ngữ")

    def _compute_readonly_domain(self):
        for rec in self:
            rec =rec.sudo()
            if rec.th_project_lpm_id.th_approve == True:
                rec.readonly_domain = json.dumps([])
            else:
                rec.readonly_domain = False
    @api.depends('th_production_standard_id')
    def _compute_th_production_standard_domain(self):
        for rec in self:
            rec = rec.sudo()
            domain = []
            if self.user_has_groups('th_lpm.group_lpm_leader') or self.user_has_groups(
                'th_lpm.group_lpm_administrator'):
                production_standard = self.env['th.production.standard'].search([('state', '=', 'approve')])
                domain.append(('id', 'in', production_standard.ids))
            elif self.user_has_groups('th_lpm.group_lpm_user'):
                production_standard = self.env['th.production.standard'].search(
                    ['|', ('create_uid', '=', self.env.uid), ('th_everybody', '=', 'True'), ('state', '=', 'approve')])
                domain.append(('id', 'in', production_standard.ids))
            rec.th_production_standard_domain = json.dumps(domain)

    @api.onchange('th_packaging_lesson_ids', 'th_number_of_credits')
    def _onchange_lesson(self):
        for rec in self:
            if rec.th_packaging_lesson_ids:
                th_packaging_lesson = 0
                for res in rec.th_packaging_lesson_ids:
                    if res.th_quantity:
                        th_packaging_lesson += res.th_quantity
                rec.th_number_of_articles = rec.th_number_of_credits * th_packaging_lesson

    @api.onchange('th_packaging_section_ids', 'th_number_of_articles')
    def _onchange_section(self):
        for rec in self:
            if rec.th_packaging_section_ids:
                th_packaging_section = 0
                for res in rec.th_packaging_section_ids:
                    if res.th_quantity:
                        th_packaging_section += res.th_quantity
                rec.th_number_of_items = rec.th_number_of_articles * th_packaging_section



    @api.model
    def _compute_production_time(self):
        for rec in self:
            rec = rec.sudo()
            rec.th_production_time = (((rec.th_number_of_articles * rec.th_lesson_duration + rec.th_number_of_items
                                        * rec.th_item_duration + rec.th_subject_duration)
                                       * rec.th_production_standard_id.th_complexity_id.th_production_duration) / 60)
            if rec.th_packaging_object_ids:
                th_packaging_object = 0
                for res in rec.th_packaging_object_ids:
                    if res.th_time:
                        th_packaging_object += res.th_time
                if  th_packaging_object > 0:
                    rec.th_day = rec.th_production_time / th_packaging_object
            else:
                rec.th_day = 0

    # @api.depends('th_packaging_days_id')
    # def _onchange_packaging_day(self):
    #     for rec in self:
    #         if rec.th_packaging_days_id:
    #             rec.th_day = rec.th_production_time / rec.th_packaging_days_id.th_time
    @api.depends('th_production_time', 'th_hourly_cost_parameter_id', 'th_percent_gv')
    def _compute_standard_costs(self):
        for rec in self:
            rec = rec.sudo()
            rec.th_standard_costs = (rec.th_percent_gv / 100) * rec.th_hourly_cost_parameter_id.th_price * rec.th_production_time

    # @api.model
    # def create(self, values):
    #     project = self.env['th.project.lpm'].search([('th_approve', '=', True)])
    #     if 'th_project_lpm_id' in values and values.get('th_project_lpm_id') in project.ids:
    #         raise ValidationError('Dự án này đã đươc phê duyệt không thể tạo thêm môn!')
    #     # if self._context.get('import_file', False):
    #     #     if values['th_packaging_lesson_ids']:
    #     #         th_packaging_lesson = self.env['th.packaging.standard'].search([('id', '=', values['th_packaging_lesson_ids'])])
    #     #         values['th_number_of_articles'] = values['th_number_of_credits'] * th_packaging_lesson.th_quantity
    #     #     if values['th_packaging_section_id']:
    #     #         th_packaging_section = self.env['th.packaging.standard'].search(
    #     #             [('id', '=', values['th_packaging_section_id'])])
    #     #         values['th_number_of_items'] = values['th_number_of_articles'] * th_packaging_section.th_quantity
    #     result = super(ThProperties, self).create(values)
    #     if self._context.get('import_file', False):
    #         for rec in result:
    #             rec._onchange_lesson()
    #             rec._onchange_section()
    #
    #     return result

    @api.model_create_multi
    def create(self, values_list):
        project_ids = self.env['th.project.lpm'].search([('th_approve', '=', True)]).ids
        for values in values_list:
            if 'th_project_lpm_id' in values and values.get('th_project_lpm_id') in project_ids:
                raise ValidationError('Dự án này đã được phê duyệt, không thể tạo thêm môn!')

        result = super().create(values_list)

        if self._context.get('import_file', False):
            for rec in result:
                rec._onchange_lesson()
                rec._onchange_section()

        return result

