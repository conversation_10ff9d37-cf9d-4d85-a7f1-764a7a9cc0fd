from odoo import models, fields, api

class ThProperties(models.Model):
    _name = 'th.value.attribute'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "<PERSON><PERSON><PERSON> trị thuộc tính thành phần học liệu"
    _rec_name = 'th_value_attribute_code'

    th_material_components_code_id = fields.Many2one(comodel_name='th.learning.material.components',
                                                     string="Mã thành phần học liệu")
    th_attribute_code_id = fields.Many2one(comodel_name='th.attribute', string="Mã thuộc tính")
    th_standard_values_id = fields.Many2one(comodel_name='th.standard.values', string="Giá trị tiêu chuẩn")

    th_production_standard_id = fields.Many2one(comodel_name='th.production.standard', string="Tiêu chuẩn sản xuất")
    th_value = fields.Integer(string="Giá trị", required=True)
    th_unit = fields.Char(string="Đơn vị tính")
    th_value_attribute_code = fields.Char(string="Mã giá trị thuộc tính")
    th_description = fields.Text(string="Mô tả")
    th_production_duration = fields.Float(string="Thời lượng sản xuất" )
    th_percentage = fields.Boolean(string="Tính theo %")

    @api.onchange('th_percentage')
    def _onchange_related(self):
        time = 0
        for rec in self:
            if rec.th_percentage == True:
               time = rec.th_production_duration / 100
            rec.th_production_duration = time




