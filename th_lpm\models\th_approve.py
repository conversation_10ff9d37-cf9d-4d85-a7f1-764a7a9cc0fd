from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
import json


class ThApproveProject(models.Model):
    _name = 'th.approve.project'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Phê duyệt dự án"
    _rec_name = 'th_project_lpm_id'

    th_project_lpm_id = fields.Many2one('th.project.lpm', string='Tên dự án')
    th_project_lpm_code = fields.Char(related="th_project_lpm_id.th_project_code", string="Mã dự án")
    th_project_scope = fields.Text(string="Phạm vi dự án", related="th_project_lpm_id.th_project_scope")
    th_implementation_unit = fields.Char(string="Đơn vị thực hiện",
                                         related="th_project_lpm_id.th_implementation_unit" )
    th_customers = fields.Many2one(related="th_project_lpm_id.th_university_id", string="Đ<PERSON><PERSON> tượng khách hàng")
    th_lead_the_project = fields.Many2one( string="Chủ trì dự án",
                                          related="th_project_lpm_id.th_lead_the_project", )
    th_project_members = fields.Many2many( string="Thành viên dự án",
                                          related="th_project_lpm_id.th_project_members", )
    th_start_date = fields.Date(string="Ngày bắt đầu",related="th_project_lpm_id.th_start_date")
    th_end_date = fields.Date(string="Ngày kết thúc",related="th_project_lpm_id.th_end_date",)
    th_production_number = fields.Integer(string="Số lượng môn", related="th_project_lpm_id.th_production_number")
    th_total_production_costs = fields.Float(related="th_project_lpm_id.th_total_production_costs")
    th_product_manufacturing_ids = fields.One2many(related="th_project_lpm_id.th_product_manufacturing_ids", string="môn")
    th_user_approve_ids = fields.One2many('th.user.approve', 'th_approve_project_id', string='Người phê duyệt',)
    state = fields.Selection(
        selection=[('draft', 'Dự thảo'),
                   ('pending', 'Chờ phê duyệt'),
                   ('approved', 'Đã phê duyệt'),
                   ('refused', 'Bị từ chối'),
                   ('cancel', 'Hủy')],
        string="Trạng thái",
        default='draft', tracking=True
    ,compute='_compute_state', group_expand='_group_expand_states', store=True)
    th_production_standard_ids = fields.Many2many('th.production.standard',
                                                  string='Tiêu chuẩn sản xuất',compute='_compute_production_standard', tracking=True)
    th_total_proposed_costs = fields.Float(string='Tổng chi phí đề xuất theo tín chỉ', related="th_project_lpm_id.th_total_proposed_costs")
    th_total_object_costs = fields.Float(string='Tổng chi phí đề xuất theo môn', related="th_project_lpm_id.th_total_object_costs")
    th_hidden_button = fields.Boolean(string='ẩn nút', default=False, compute='_compute_th_hidden_button')
    th_cost_qa = fields.Float(string='Chi Phí QA',related="th_project_lpm_id.th_cost_qa")
    th_costs_incurred = fields.Float(string='Chi phí phát sinh',related="th_project_lpm_id.th_costs_incurred")
    # th_percent_costs_incurred = fields.Float(related="th_project_lpm_id.th_percent_costs_incurred")
    # th_selection_costs = fields.Selection(related="th_project_lpm_id.th_selection_costs")
    # th_percent_cost_qa = fields.Float(related="th_project_lpm_id.th_percent_cost_qa")
    th_state_approve = fields.Many2many("res.users", compute='_compute_th_state',string="Người đã phê duyệt")
    th_state_pending = fields.Many2many("res.users", compute='_compute_th_state',string="Người chưa phê duyệt")
    th_is_lpm2 = fields.Boolean(related="th_project_lpm_id.th_is_lpm2", string="Đã chuyển sang LPM2", default=False)

    # Thêm trường đội nhóm và domain
    th_team_id = fields.Many2one('th.team', string='Đội nhóm')
    th_lead_domain = fields.Char(compute='_compute_lead_domain', string='Domain chủ trì')
    th_members_domain = fields.Char(compute='_compute_members_domain', string='Domain thành viên')

    # Thêm trường riêng để chọn chủ trì và thành viên từ đội nhóm
    th_selected_lead = fields.Many2one('res.users', string='Chủ trì dự án (từ đội nhóm)')
    th_selected_members = fields.Many2many('res.users', 'th_approve_project_selected_members_rel', 'approve_id', 'user_id', string='Thành viên dự án (từ đội nhóm)')
    
    @api.depends('th_user_approve_ids.status')
    def _compute_th_state(self):
        for record in self:
            approved_users = record.th_user_approve_ids.filtered(lambda r: r.status == 'approved')
            pending_users = record.th_user_approve_ids.filtered(lambda r: r.status == 'pending')
            record.th_state_approve = [(6,0,approved_users.mapped('th_user_id').ids)]
            record.th_state_pending = [(6,0,pending_users.mapped('th_user_id').ids)]

    @api.depends('th_team_id')
    def _compute_lead_domain(self):
        """Tính domain cho chủ trì dự án dựa trên đội nhóm đã chọn"""
        for rec in self:
            if rec.th_team_id:
                # Lấy tất cả trưởng nhóm và thành viên trong đội nhóm
                user_ids = []
                if rec.th_team_id.th_manager_id:
                    user_ids.append(rec.th_team_id.th_manager_id.id)
                if rec.th_team_id.th_user_ids:
                    user_ids.extend(rec.th_team_id.th_user_ids.ids)
                # Loại bỏ trùng lặp
                user_ids = list(set(user_ids))
                domain = [('id', 'in', user_ids)]
            else:
                domain = []
            rec.th_lead_domain = json.dumps(domain)

    @api.depends('th_team_id', 'th_selected_lead')
    def _compute_members_domain(self):
        """Tính domain cho thành viên dự án dựa trên đội nhóm và chủ trì đã chọn"""
        for rec in self:
            if rec.th_team_id:
                # Lấy tất cả trưởng nhóm và thành viên trong đội nhóm
                user_ids = []
                if rec.th_team_id.th_manager_id:
                    user_ids.append(rec.th_team_id.th_manager_id.id)
                if rec.th_team_id.th_user_ids:
                    user_ids.extend(rec.th_team_id.th_user_ids.ids)
                # Loại bỏ trùng lặp
                user_ids = list(set(user_ids))
                # Loại bỏ chủ trì đã chọn
                if rec.th_selected_lead:
                    user_ids = [uid for uid in user_ids if uid != rec.th_selected_lead.id]
                domain = [('id', 'in', user_ids)]
            else:
                domain = []
            rec.th_members_domain = json.dumps(domain)

    @api.depends('th_production_standard_ids')
    def _compute_th_hidden_button(self):
        domain = self.th_user_approve_ids.mapped('th_user_id.id')
        for rec in self:
            rec.th_hidden_button =True if self.env.user.id in domain else False

    @api.depends('th_project_lpm_id')
    def _compute_production_standard(self):
        for rec in self:
            standard = []
            if rec.th_project_lpm_id.th_product_manufacturing_ids:
                # Lấy tất cả production standards và sắp xếp theo tên
                production_standards = rec.th_project_lpm_id.th_product_manufacturing_ids.mapped(
                    'th_production_standard_id')
                sorted_standards = production_standards.sorted(key=lambda r: r.th_standard_code)
                standard.extend(sorted_standards.ids)

            rec.th_production_standard_ids = standard if standard else [(5, 0, 0)]

    def action_approved(self):
        for rec in self.th_user_approve_ids:
            if self.env.user.id == rec.th_user_id.id:
                rec.status = 'approved'
        if not self.th_user_approve_ids.filtered(lambda d: d.status != 'approved'):
            self.state = 'approved'
        else:
            return True

    def action_refuse(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'refused'
            self.state = 'refused'
    def action_draft(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'draft'
        self.state = 'draft'
        return True



    def action_pending(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'pending'
        self.state = 'pending'
        return True

    def action_cancel(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'cancel'
        self.state = 'cancel'
        return True

    @api.depends("th_user_approve_ids")
    def _compute_state(self):
        for rec in self:
            if rec.th_user_approve_ids:
                statuses = self.th_user_approve_ids.mapped('status')
                if self.ids == False:
                    self.state = 'draft'
                elif 'refused' in statuses:
                    self.state = 'refused'
                elif all(status == 'approved' for status in statuses):
                    self.state = 'approved'

                else:
                    self.state = 'draft'
                # if rec.env.user in rec.th_user_approve_ids.mapped('th_user_id'):
                #     rec.state = rec.th_user_approve_ids.filtered(lambda d: d.th_user_id == rec.env.user)[0].status
                # else:
                #     for approver in rec.th_user_approve_ids:
                #         rec.state = approver.status
            else:
                rec.state = 'pending'
    def _group_expand_states(self, states, domain, order):
        return [key for key, val in type(self).state.selection]

    @api.onchange('th_team_id')
    def _onchange_th_team_id(self):
        """Khi chọn đội nhóm, reset chủ trì và thành viên dự án"""
        # Reset các trường chọn
        self.th_selected_lead = False
        self.th_selected_members = [(5, 0, 0)]

    @api.onchange('th_selected_lead')
    def _onchange_th_selected_lead(self):
        """Khi chọn chủ trì, cập nhật vào th_project_lpm_id và reset thành viên"""
        if self.th_project_lpm_id and self.th_selected_lead:
            # Cập nhật chủ trì vào project
            self.th_project_lpm_id.th_lead_the_project = self.th_selected_lead.id
        # Reset thành viên dự án
        self.th_selected_members = [(5, 0, 0)]

    @api.onchange('th_selected_members')
    def _onchange_th_selected_members(self):
        """Khi chọn thành viên, cập nhật vào th_project_lpm_id"""
        if self.th_project_lpm_id and self.th_selected_members:
            # Cập nhật thành viên vào project
            self.th_project_lpm_id.th_project_members = [(6, 0, self.th_selected_members.ids)]

    def write(self, values):
        # Đồng bộ dữ liệu từ trường chọn sang project
        if 'th_selected_lead' in values and values.get('th_selected_lead'):
            for rec in self:
                if rec.th_project_lpm_id:
                    rec.th_project_lpm_id.th_lead_the_project = values['th_selected_lead']

        if 'th_selected_members' in values:
            for rec in self:
                if rec.th_project_lpm_id:
                    rec.th_project_lpm_id.th_project_members = values['th_selected_members']

        res = super(ThApproveProject, self).write(values)
        for rec in self:
            if values.get('state') == 'approved':
                rec.th_project_lpm_id.th_approve = True
            elif 'state' in values and values.get('state') != 'approved':
                rec.th_project_lpm_id.th_approve = False
        return res

    @api.model_create_multi
    def create(self, values_list):
        records = super(ThApproveProject, self).create(values_list)
        # Đồng bộ dữ liệu từ project sang trường chọn
        for rec in records:
            if rec.th_project_lpm_id:
                if rec.th_project_lpm_id.th_lead_the_project:
                    rec.th_selected_lead = rec.th_project_lpm_id.th_lead_the_project.id
                if rec.th_project_lpm_id.th_project_members:
                    rec.th_selected_members = [(6, 0, rec.th_project_lpm_id.th_project_members.ids)]
        return records

    @api.onchange('th_project_lpm_id')
    def _onchange_th_project_lpm_id(self):
        """Khi chọn dự án, đồng bộ dữ liệu sang trường chọn"""
        if self.th_project_lpm_id:
            if self.th_project_lpm_id.th_lead_the_project:
                self.th_selected_lead = self.th_project_lpm_id.th_lead_the_project.id
            if self.th_project_lpm_id.th_project_members:
                self.th_selected_members = [(6, 0, self.th_project_lpm_id.th_project_members.ids)]

    def action_th_products_approve_manufacturing(self):
        for rec in self:
            action = self.env["ir.actions.actions"]._for_xml_id("th_lpm.th_approve_products_manufacturing_action")
            action['domain'] = [('id', 'in', rec.th_product_manufacturing_ids.ids)]
            action['context'] = {'default_th_project_lpm_id': rec.th_project_lpm_id.id,'create': False}
        return action
    
    
    def action_th_transfer_lpm2(self):
        """
        Chuyển toàn bộ danh sách học phần từ LPM1 sang LPM2
        """
        if self.th_is_lpm2:
            raise UserError("Dự án này đã được chuyển sang LPM2 trước đó!")
        
        if self.state != 'approved':
            raise UserError("Chỉ có thể chuyển dự án đã được phê duyệt sang LPM2!")
        
        # Tạo dự án trong LPM2 nếu chưa tồn tại
        lpm2_project = self.env['th.project.lpm2'].search([
            ('th_project_code', '=', self.th_project_lpm_id.th_project_code)
        ], limit=1)
        if not lpm2_project:
            lpm2_project = self.env['th.project.lpm2'].create({
                'name': self.th_project_lpm_id.name,
                'th_project_code': self.th_project_lpm_id.th_project_code,
                'th_description': self.th_project_lpm_id.th_description,
                'th_project_manager_id': self.th_project_lpm_id.th_lead_the_project.id if self.th_project_lpm_id.th_lead_the_project else False,
                'th_member_ids': [(6, 0, self.th_project_lpm_id.th_project_members.ids)],
                'th_start_date': self.th_project_lpm_id.th_start_date,
                'th_end_date': self.th_project_lpm_id.th_end_date,
                'th_origin_id': self.th_project_lpm_id.th_university_id.id if self.th_project_lpm_id.th_university_id else False,
            })
        
        # Tạo các học phần trong LPM2
        for manufacturing in self.th_product_manufacturing_ids:
            course_data = {
                'th_project_template_id': manufacturing.th_project_template_id.id if manufacturing.th_project_template_id else False,
                'th_course_code': manufacturing.th_term_code or manufacturing.th_course_code,
                'th_production_standard': manufacturing.th_production_standard_id.th_description_attribute if manufacturing.th_production_standard_id else '',
                'th_project_id': lpm2_project.id,
                'th_aum_code': manufacturing.th_default_code,
                'th_credit': manufacturing.th_number_of_credits,
                'th_type': manufacturing.th_type,
                'th_language': manufacturing.th_language,
                'th_object_type': manufacturing.th_object_type,
                'th_origin_id': manufacturing.th_units_used_id.id if manufacturing.th_units_used_id else False,
                'th_major_ids': [(6, 0, manufacturing.th_major_ids.ids)],
            }
            
            # Tạo học phần trong LPM2
            course = self.env['th.course.lpm2'].create(course_data)
            
        # Đánh dấu đã chuyển sang LPM2
        self.th_project_lpm_id.th_is_lpm2 = True
        
        # Ghi log note tại LPM1
        self.message_post(
            body=f"Dự án đã được chuyển sang LPM2 với mã dự án: {lpm2_project.th_project_code}. "
                 f"Tổng số {len(self.th_product_manufacturing_ids)} học phần đã được tạo trong LPM2.",
            message_type='notification'
        )
        
        # Ghi log note tại LPM2
        lpm2_project.message_post(
            body=f"Dự án được chuyển từ LPM1 với {len(self.th_product_manufacturing_ids)} học phần. "
                 f"Dự án gốc LPM1: {self.th_project_lpm_id.th_project_code}",
            message_type='notification'
        )
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Thành công!',
                'message': f'Đã chuyển thành công {len(self.th_product_manufacturing_ids)} học phần sang LPM2',
                'type': 'success',
                'sticky': False,
            }
        }
    
class ApprovalCategory(models.Model):
    _inherit = 'approval.category'

    def create_request_product(self):
        action = {
            "name": _("Phê duyệt sản phẩm"),
            "type": "ir.actions.act_window",
            "res_model": "approval.request",
            "views": [[self.env.ref("th_lpm.approval_request_product_view_tree").id, "tree"], [False, "form"]],
            "domain": [('category_id', "=",  self.env.ref('th_lpm.approval_product_category_data').id)],
            "context": {
                'create': True,
                'form_view_initial_mode': 'edit',
                'default_name': _('New') if self.automated_sequence else self.name,
                'default_category_id': self.env.ref('th_lpm.approval_product_category_data').id,
                'default_request_owner_id': self.env.user.id,
                'default_request_status': 'new'
            },
        }

        return action

