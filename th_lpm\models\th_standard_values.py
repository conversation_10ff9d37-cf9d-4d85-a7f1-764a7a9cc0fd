from odoo import models, fields,api
import json

class Th<PERSON><PERSON><PERSON>d<PERSON><PERSON><PERSON>(models.Model):
    _name = 'th.standard.values'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "<PERSON>i<PERSON> trị tiêu chuẩn"
    # _rec_name = 'th_attribute_code'

    th_material_components_code_id = fields.Many2one(comodel_name='th.learning.material.components', string="Mã thành phần học liệu",)
    th_production_standard_id = fields.Many2one(comodel_name='th.production.standard', string="Tiêu chuẩn sản xuất")
    th_value_attribute_ids = fields.Many2many("th.value.attribute", 'th_standard_values_id', string="Giá trị thuộc tính")
    th_value_attribute_percentage_id = fields.Many2one("th.value.attribute", string="Giá trị thuộc tính %")
    th_detailed_duration = fields.Float("Thời lượng chi tiết", compute="_compute_detailed_duration")
    th_value_attribute_main = fields.Char(compute="_compute_th_value_attribute_main")
    th_value_attribute_percentage_domain = fields.Char(compute="_compute_th_value_attribute_main")




    @api.depends('th_material_components_code_id')
    def _compute_th_value_attribute_main(self):
        for rec in self:
            domain = []
            domain_percentage = []
            value_attribute = self.env['th.value.attribute'].search([('th_material_components_code_id', '=', rec.th_material_components_code_id.id), ('th_percentage', '!=', True)])
            value_attribute_percentage = self.env['th.value.attribute'].search([('th_material_components_code_id', '=', rec.th_material_components_code_id.id), ('th_percentage', '=', True)])
            domain.append(('id', 'in', value_attribute.ids))
            domain_percentage.append(('id', 'in', value_attribute_percentage.ids))
            rec.th_value_attribute_main = json.dumps(domain)
            rec.th_value_attribute_percentage_domain = json.dumps(domain_percentage)

    @api.depends('th_value_attribute_ids')
    def _compute_detailed_duration(self):
        for rec in self:
            if rec.th_production_standard_id:
                value = 0
                for record in rec.th_value_attribute_ids:
                    value += record.th_production_duration
                if rec.th_value_attribute_percentage_id.th_production_duration:
                    rec.th_detailed_duration = value * rec.th_value_attribute_percentage_id.th_production_duration
                elif not rec.th_value_attribute_percentage_id.th_production_duration:
                    rec.th_detailed_duration = value


