from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json
class ThUserApprove(models.Model):
    _name = 'th.user.approve'
    _description = "Phê duyệt dự án"
    
    th_user_id = fields.Many2one('res.users', string="Người phê duyệt")
    is_required = fields.Boolean("bắt buộc")
    status = fields.Selection([('draft', 'Nháp'), ('pending', 'Chờ phê duyệt'), ('approved', 'Đã phê duyệt'),
                               ('refused', 'Từ chối'), ('cancel', 'Hủy')], string='Trạng thái phê duyệt', default='draft')
    th_approve_project_id = fields.Many2one('th.approve.project', string="Phê duyệt dự án")

    @api.depends('th_user_id')
    def _compute_display_name(self):
        for rec in self:
            if rec.th_user_id.name:
                rec.display_name = f"{rec.th_user_id.name}"


