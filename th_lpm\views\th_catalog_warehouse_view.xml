<odoo>
<!--<record id="th_catalog_warehouse_view_kanban" model="ir.ui.view">-->
<!--        <field name="name">th_catalog_warehouse_view_kanban</field>-->
<!--        <field name="model">th.catalog.warehouse</field>-->
<!--        <field name="arch" type="xml">-->
<!--            <kanban type="object">-->
<!--                <field name="name"/>-->
<!--                <field name="th_description"/>-->
<!--                <templates>-->
<!--                    <t t-name="kanban-box">-->
<!--                        <div t-attf-class="oe_kanban_global_click">-->
<!--                            <div t-attf-class="o_kanban_card_header">-->
<!--                                <div class="o_kanban_card_header_title">-->
<!--                                    <div class="o_kanban_record_title oe_kanban_details">-->
<!--                                        <strong><h4><field name="name"/></h4></strong>-->
<!--                                    </div>-->
<!--                                    <div t-if="record.th_description">-->
<!--                                        <i class="fa fa-info-circle" title="Description" role="img" aria-label="Description"></i>Description-->
<!--                                        <t t-esc="record.th_description.value"/>-->
<!--                                    </div>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </t>-->
<!--                </templates>-->
<!--            </kanban>-->
<!--        </field>-->
<!--    </record>-->

<!--    <record id="th_catalog_warehouse_view_kanban_action" model="ir.actions.act_window">-->
<!--        <field name="name">Danh mục học liệu</field>-->
<!--        <field name="res_model">th.catalog.warehouse</field>-->
<!--        <field name="view_mode">kanban,form</field>-->
<!--        <field name="context">{'create': False}</field>-->
<!--&lt;!&ndash;        <field name="view_id" ref="th_catalog_warehouse_view_kanban"/>&ndash;&gt;-->
<!--        <field name="help" type="html">-->
<!--          <p class="oe_view_nocontent_create">-->
<!--            &lt;!&ndash; Add Text Here &ndash;&gt;-->
<!--          </p><p>-->
<!--            &lt;!&ndash; More details about what a user can do with this object will be OK &ndash;&gt;-->
<!--          </p>-->
<!--        </field>-->
<!--    </record>-->
      <record id="th_catalog_warehouse_tree_view" model="ir.ui.view">
        <field name="name">Danh mục Học liệu</field>
        <field name="model">th.catalog.warehouse</field>
        <field name="arch" type="xml">
            <tree string="Danh mục Học liệu" editable="bottom">
                <field name="name"/>
                <field name="th_description"/>
            </tree>
        </field>
    </record>

    <record id="th_catalog_warehouse_tree_action" model="ir.actions.act_window">
        <field name="name">Danh mục Học liệu</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.catalog.warehouse</field>
        <field name="view_mode">tree</field>
    </record>
</odoo>