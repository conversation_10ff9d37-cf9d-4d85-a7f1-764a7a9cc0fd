import odoo.tools
from odoo import models, fields,api
import json


class ThProductionStandard(models.Model):
    _name = 'th.production.standard'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Tiêu chuẩn học liệu"
    _rec_name = 'th_standard_code'


    th_standard_code = fields.Char(string="Mã tiêu chuẩn")
    th_production_version = fields.Char(string="Phiên bản sản xuất", required=True,copy=True)
    th_unit_use = fields.Char(string="Đơn vị sử dụng", copy=True)
    th_description = fields.Text(string="<PERSON>ô tả", copy=True)
    th_standard_values_ids = fields.One2many("th.standard.values", "th_production_standard_id",
                                             string="Giá trị tiêu chuẩn",tracking=True)
    th_estimated_time = fields.Float(string="Thời gian ước tính", )
    th_product_manufacturing_id = fields.Many2one("th.product.manufacturing", string="<PERSON><PERSON><PERSON> l<PERSON> sản xuất",tracking=True)
    th_packaging_standard_id = fields.Many2one(comodel_name='th.packaging.standard', string="Tham số đóng gói",tracking=True,default=lambda self: self.env.ref('th_lpm.lpm_parameter_01').id,)
    th_complexity_id = fields.Many2one("th.value.attribute", string="Độ phức tạp",tracking=True,copy=True)
    state = fields.Selection(
        selection=[('draft', 'Nháp'),
                   ('pending', 'Chờ duyệt'),
                   ('approve', 'Đã duyệt'),
                   ('refuse', 'Từ chối'),
                   ('cancel', 'Hủy')],
        string="Trạng thái",
        default='draft', tracking=True
    )
    th_complexity_id_domain = fields.Char(compute="_compute_th_complexity_id_main")
    th_subject_duration = fields.Float(string="Thời lượng môn", compute="_compute_duration")
    th_lesson_duration = fields.Float(string="Thời lượng bài", readonly=1)
    th_item_duration = fields.Float(string="Thời lượng mục", readonly=1)
    th_value_attribute_ids = fields.Many2many("th.value.attribute", string="Giá trị thuộc tính",compute="_compute_value_attribute",tracking=True)
    th_description_attribute = fields.Html(string="Mô tả giá trị thuộc tính",compute="_compute_description_attribute")
    th_everybody = fields.Boolean()
    th_interpretation = fields.Text(string="Diễn giải")
    th_subjects_used = fields.Integer(string="Số môn dùng", compute="_compute_subjects_used")
    # readonly_domain = fields.Char(compute="_compute_readonly_domain")

    # def _compute_readonly_domain(self):
    #     for rec in self:
    #         if rec.state  in ['refuse','cancel']:
    #             rec.readonly_domain = json.dumps([])
    #         else:
    #             rec.readonly_domain = False
    @api.model
    def _compute_subjects_used(self):
        for record in self:
                record.th_subjects_used = len(self.env['th.product.manufacturing'].search([('th_production_standard_id', '=', record.id)]))

    @api.depends('th_standard_values_ids')
    def _compute_value_attribute(self):
        for record in self:
            if record.th_standard_values_ids:
                all_attributes = (
                        record.th_standard_values_ids.th_value_attribute_ids +
                        record.th_standard_values_ids.th_value_attribute_percentage_id +
                        record.th_complexity_id
                )
                sorted_attributes = self.env['th.value.attribute'].browse(
                    all_attributes.ids
                ).sorted(key=lambda r: r.th_value_attribute_code)

                record.th_value_attribute_ids = sorted_attributes

    @api.depends('th_value_attribute_ids')
    def _compute_description_attribute(self):
        for record in self:
            descriptions = []
            for value_record in record.th_value_attribute_ids:
                descriptions.append(f"<b>{value_record.th_value_attribute_code}</b>: {value_record.th_description}")
            record.th_description_attribute = ',<br/> '.join(descriptions)

    @api.model
    def default_get(self, default_fields):
        res = super().default_get(default_fields)
        data = self.env['th.learning.material.components'].search(
            [('id', '!=', self.env.ref('th_lpm.lpm_professional_content').id)])
        res['th_standard_values_ids'] = [(0, 0, {'th_material_components_code_id': comp_id}) for
                                         comp_id in data]
        return res

    @api.model_create_multi
    def create(self, values_list):
        res = super().create(values_list)
        return res

    @api.depends('th_standard_values_ids')
    def _compute_duration(self):
        ingredient = [
            self.env.ref('th_lpm.lpm_electronic_lectures'),
            self.env.ref('th_lpm.lpm_electronic_textbook'),
            self.env.ref('th_lpm.lpm_vocabulary'),
            self.env.ref('th_lpm.lpm_dictionary_of_terms')
        ]
        for res in self:
            subject = 0.0
            lesson = 0.0
            item = 0.0
            for record in res.th_standard_values_ids:
                if record.th_material_components_code_id == self.env.ref('th_lpm.lpm_outline'):
                    subject += record.th_detailed_duration * res.th_complexity_id.th_production_duration
                elif record.th_material_components_code_id == self.env.ref('th_lpm.lpm_test'):
                    if res.th_packaging_standard_id:
                        subject += record.th_detailed_duration * res.th_packaging_standard_id.th_quantity
                    else:
                        subject += record.th_detailed_duration
                elif record.th_material_components_code_id == self.env.ref('th_lpm.lpm_question_bank'):
                    lesson = record.th_detailed_duration
                elif record.th_material_components_code_id in ingredient:
                    item += record.th_detailed_duration
            res.th_subject_duration = subject
            res.th_lesson_duration = lesson
            res.th_item_duration = item

    @api.depends('th_complexity_id')
    def _compute_th_complexity_id_main(self):
        for rec in self:
            domain = []
            value_complexity = self.env['th.value.attribute'].search(
                [('th_material_components_code_id', '=', self.env.ref('th_lpm.lpm_professional_content').id),
                 ('th_percentage', '=', True)])
            domain.append(('id', 'in', value_complexity.ids))
            rec.th_complexity_id_domain = json.dumps(domain)

    def action_send_for_approval(self):
        self.state = 'pending'
        return True

    def action_approved(self):
        self.state = 'approve'
        return True

    def action_refuse(self):
        self.state = 'refuse'
        return True

    def action_draft(self):
        self.state = 'draft'
        return True
    def action_cancel(self):
        self.state = 'cancel'
        return True
    @api.model
    def create(self, values):
        if self.user_has_groups('th_lpm.group_lpm_leader'):
            values['th_everybody'] = True
        rec = super(ThProductionStandard, self).create(values)
        # Add code here
        return rec
