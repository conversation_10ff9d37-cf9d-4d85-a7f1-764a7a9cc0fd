# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo import models


class IrUiMenu(models.Model):
    _inherit = 'ir.ui.menu'

    def _load_menus_blacklist(self):
        res = super()._load_menus_blacklist()
        if self.env.user.has_group('hr_recruitment.group_hr_recruitment_interviewer'):
            res.append(self.env.ref('hr_recruitment.menu_hr_job_position').id)
        return res
