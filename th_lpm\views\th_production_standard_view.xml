<odoo>
      <record id="th_production_standard_tree_view" model="ir.ui.view">
        <field name="name">th_production_standard_tree_view</field>
        <field name="model">th.production.standard</field>
        <field name="arch" type="xml">
            <tree string="tiêu chuẩn học liệu" >
                <field name="th_standard_code" />
                <field name="th_interpretation"/>
                <field name="th_production_version"/>
                <field name="th_unit_use"/>
                <field name="th_subjects_used"/>
                <field name="state"/>
            </tree>
        </field>
    </record>

    <record id="th_production_standard_form_view" model="ir.ui.view">
        <field name="name">th_production_standard_form_view</field>
        <field name="model">th.production.standard</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_send_for_approval" type="object" string="Gửi duy<PERSON>" states="draft" class="btn-primary"/>
                    <button name="action_approved" type="object" string="Duyệt" states="pending" class="btn-primary" groups="th_lpm.group_lpm_leader" confirm="Bạn có chắc muốn duyệt tiêu chuẩn học liệu này không này?"/>
                    <button name="action_refuse" type="object" string="Từ chối" states="pending" class="btn-primary" groups="th_lpm.group_lpm_leader" confirm="Bạn có chắc muốn từ chối tiêu chuẩn học liệu này?"/>
                    <button name="action_draft" type="object" string="Nháp"  class="btn-primary"  confirm="Bạn có muốn đưa về trạng thái nháp tiêu chuẩn học liệu này?" attrs="{'invisible': [('state', 'not in', ['refuse','cancel'])]}"/>
                    <button name="action_cancel" type="object" string="Hủy"  class="btn-primary"  confirm="Bạn có muốn đưa về trạng thái hủy tiêu chuẩn học liệu này?" attrs="{'invisible': [('state', '=', 'cancel')]}"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,pending,approve,refuse,cancel"/>
                </header>
                <sheet>
<!--                    <field name="readonly_domain" invisible="1"/>-->
                    <group>
                        <group>
                            <field name="th_standard_code" required="1"  attrs="{'readonly': [('state', '!=','draft')]}"/>
                            <field name="th_production_version" attrs="{'readonly': [('state', '!=','draft')]}"/>
                            <field name="th_unit_use" attrs="{'readonly': [('state', '!=','draft')]}"/>
                            <field name="th_interpretation" attrs="{'readonly': [('state', '!=','draft')]}"/>
                            <field name="th_subjects_used"/>
                        </group>
                        <group name='group_right'>
                            <field name="th_packaging_standard_id" attrs="{'readonly': [('state', '!=','draft')]}"   options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_complexity_id" domain="th_complexity_id_domain" attrs="{'readonly': [('state', '!=','draft')]}"   options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                            <field name="th_complexity_id_domain" invisible="1"/>
                            <field name="th_subject_duration"  string="Thời lượng môn (Đề cương, bài kiểm tra)"/>
                            <field name="th_lesson_duration" string="Thời lượng bài (Ngân hàng câu hỏi)"/>
                            <field name="th_item_duration" string="Thời lượng mục (Giáo trình Text, bài giảng Slide,từ vựng, thuật ngữ)"/>
                            <field name="th_everybody" invisible="1"/>

                        </group>
                    </group>
                    <notebook>
                        <page string="Chi tiết giá trị tiêu chuẩn">
                            <field name="th_standard_values_ids" attrs="{'readonly': [('state', '!=','draft')]}" >
                                <tree editable="bottom" create='0' delete="0" no_open="1">
                                    <field name="th_material_components_code_id" options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                                    <field name="th_value_attribute_ids" widget="many2many_tags"
                                           domain="th_value_attribute_main"  options="{'no_create': True,'no_edit': True, 'no_open':True}"/>
                                    <field name="th_value_attribute_percentage_id"
                                           domain="th_value_attribute_percentage_domain" options="{'no_create': True,'no_edit': True, 'no_open':True}" />
                                    <field name="th_value_attribute_main" invisible="1"/>
                                    <field name="th_value_attribute_percentage_domain" invisible="1"/>
                                    <field name="th_detailed_duration" />
                                </tree>
                            </field>
                        </page>
                        <page string="Mô tả">
                            <field name="th_value_attribute_ids">
                                <tree create="0" edit="0" delete="0" no_open="1">
                                    <field name="th_material_components_code_id"/>
                                    <field name="th_value_attribute_code"/>
                                    <field name="th_description"/>
                                </tree>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <div class="oe_chatter">
                    <field name="message_follower_ids"/>
                    <field name="activity_ids"/>
                    <field name="message_ids"/>
                </div>
            </form>
        </field>
    </record>
    <record id="th_production_standard_action" model="ir.actions.act_window">
        <field name="name">Tiêu chuẩn học liệu</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">th.production.standard</field>
        <field name="view_mode">tree,form</field>
    </record>


</odoo>